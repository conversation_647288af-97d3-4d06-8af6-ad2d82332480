<!DOCTYPE html>
<html>
<head>
    <title>Image Placeholder Generator</title>
</head>
<body>
    <script>
        // Function to create placeholder images
        function createPlaceholderImage(width, height, text, filename) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = width;
            canvas.height = height;
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, width, height);
            gradient.addColorStop(0, '#d18232');
            gradient.addColorStop(0.5, '#033f88');
            gradient.addColorStop(1, '#1f2633');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);
            
            // Add text
            ctx.fillStyle = 'white';
            ctx.font = `${Math.min(width, height) / 10}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, width / 2, height / 2);
            
            // Convert to blob and download
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                a.click();
                URL.revokeObjectURL(url);
            }, 'image/jpeg', 0.8);
        }
        
        // Generate all placeholder images
        const images = [
            { width: 1920, height: 1080, text: 'Sky & Clouds Background', filename: 'sky-clouds-background.jpg' },
            { width: 800, height: 600, text: 'Spišská Nová Ves Aerial View', filename: 'spiska-nova-ves-aerial.jpg' },
            { width: 800, height: 600, text: 'Accuracy Landing Competition', filename: 'accuracy-landing.jpg' },
            { width: 800, height: 600, text: 'Individual Style Competition', filename: 'individual-style.jpg' },
            { width: 400, height: 300, text: 'Hotel Metropol', filename: 'hotel-metropol.jpg' },
            { width: 400, height: 300, text: 'Park Hotel', filename: 'park-hotel.jpg' },
            { width: 400, height: 300, text: 'Hotel Metropol Room', filename: 'hotel-metropol-2.jpg' },
            { width: 400, height: 300, text: 'Park Hotel Room', filename: 'park-hotel-2.jpg' },
            { width: 1200, height: 800, text: 'Championship Gallery', filename: 'championship-gallery-1.jpg' },
            { width: 800, height: 600, text: 'Spišská Nová Ves Landscape', filename: 'spiska-nova-ves-landscape.jpg' }
        ];
        
        // Create download buttons
        images.forEach(img => {
            const button = document.createElement('button');
            button.textContent = `Download ${img.filename}`;
            button.onclick = () => createPlaceholderImage(img.width, img.height, img.text, img.filename);
            document.body.appendChild(button);
            document.body.appendChild(document.createElement('br'));
        });
    </script>
    
    <h1>Placeholder Image Generator</h1>
    <p>Click the buttons below to download placeholder images for your website:</p>
</body>
</html>
