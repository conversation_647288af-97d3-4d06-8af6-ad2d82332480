<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>World Skydiving Championship 2026 - Spišská Nová Ves</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>

    <!-- Navigation -->
        <nav class="navigation bg-white/95 backdrop-blur-lg shadow-xl sticky top-0 z-50 border-b border-gray-100">
        <div class="nav-container max-w-7xl mx-auto flex justify-between items-center py-4 px-6">
            <!-- Logo/Brand -->
            <div class="nav-brand flex items-center gap-3">
                <div class="brand-icon text-white p-3 rounded-xl shadow-lg" style="background-color: #6acef3;">
                    <span class="text-2xl">🪂</span>
                </div>
                <div class="brand-text flex items-center gap-3">
                    <h1 class="text-xl font-bold text-gray-900">World Championship</h1>
                    <span class="text-gray-400">•</span>
                    <p class="text-sm text-gray-600 font-medium">Skydiving 2026</p>
                </div>
            </div>

            <!-- Navigation Links -->
            <div class="nav-links hidden md:flex items-center gap-6">
                <a href="#home" class="nav-link text-gray-700 font-medium transition-colors duration-300" style="color: #556e78;" onmouseover="this.style.color='#6acef3'" onmouseout="this.style.color='#556e78'">Home</a>
                <a href="#disciplines" class="nav-link text-gray-700 font-medium transition-colors duration-300" style="color: #556e78;" onmouseover="this.style.color='#6acef3'" onmouseout="this.style.color='#556e78'">Disciplines</a>
                <a href="#location" class="nav-link text-gray-700 font-medium transition-colors duration-300" style="color: #556e78;" onmouseover="this.style.color='#6acef3'" onmouseout="this.style.color='#556e78'">Location</a>
                <a href="#accommodation" class="nav-link text-gray-700 font-medium transition-colors duration-300" style="color: #556e78;" onmouseover="this.style.color='#6acef3'" onmouseout="this.style.color='#556e78'">Hotels</a>
                <a href="#gallery" class="nav-link text-gray-700 font-medium transition-colors duration-300" style="color: #556e78;" onmouseover="this.style.color='#6acef3'" onmouseout="this.style.color='#556e78'">Gallery</a>
            </div>

            <!-- Action Buttons -->
            <div class="nav-actions flex items-center gap-3">
                <button class="info-btn hidden sm:flex items-center gap-2 font-medium px-4 py-2 rounded-xl transition-all duration-300" style="color: #556e78;" onmouseover="this.style.color='#6acef3'; this.style.backgroundColor='#b2e0e0'" onmouseout="this.style.color='#556e78'; this.style.backgroundColor='transparent'">
                    <span class="text-lg">ℹ️</span>
                    <span>Info</span>
                </button>
                <button class="register-btn text-white font-bold px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300 flex items-center gap-2" style="background-color: #6acef3;" onmouseover="this.style.backgroundColor='#94a3d6'" onmouseout="this.style.backgroundColor='#6acef3'">
                    <span class="text-lg">📝</span>
                    <span>Register</span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero relative h-screen flex items-center justify-center overflow-hidden pt-16">
        <!-- Background Image -->
        <div class="hero-background absolute inset-0 z-0">
            <img src="images/sky-clouds-background.jpg" 
                 alt="Sky with clouds background" 
                 class="hero-bg-img w-full h-full object-cover">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-900/40 via-gray-900/30 to-black/50"></div>
        </div>

        <!-- Hero Content -->
        <div class="hero-content relative z-10 text-center max-w-5xl mx-auto px-6 -mt-16">
            <!-- Logo and Title Section -->
            <div class="hero-header-section flex flex-col md:flex-row items-center justify-center gap-4 md:gap-6 mb-3">
                <!-- Championship Logo -->
                <div class="hero-logo-section">
                    <div class="logo-container bg-white/10 backdrop-blur-md border border-white/20 rounded-3xl p-4 shadow-2xl">
                        <img src="images/logo.jpg" alt="World Skydiving Championship 2026 Logo" class="logo-img w-16 h-16 md:w-24 md:h-24 object-contain rounded-2xl shadow-xl">
                    </div>
                </div>

                <!-- Main Title -->
                <div class="hero-header">
                    <h1 class="hero-title text-2xl md:text-4xl xl:text-5xl font-black text-white leading-tight mb-2">
                        <span class="block" style="color: #b2e0e0;">WORLD</span>
                        <span class="block text-white">SKYDIVING</span>
                        <span class="block" style="color: #ffc828;">CHAMPIONSHIP</span>
                    </h1>
                    <div class="hero-year text-lg md:text-xl font-bold" style="color: #94a3d6;">2026</div>
                </div>
            </div>

            <!-- Description -->
            <p class="hero-description text-base md:text-lg text-gray-200 max-w-3xl mx-auto leading-relaxed mb-4">
                Witness the ultimate test of precision and artistry as the world's elite skydivers compete for glory in the heart of Slovakia. Every jump, every landing, every movement defines champions.
            </p>

            <!-- Event Info Cards -->
            <div class="hero-info grid grid-cols-1 md:grid-cols-3 gap-3 mb-4 max-w-3xl mx-auto">
                <div class="info-card backdrop-blur-md border border-white/20 rounded-2xl p-3 text-center" style="background-color: rgba(106, 206, 243, 0.1);">
                    <div class="info-icon text-xl mb-1">📅</div>
                    <h3 class="text-xs font-bold text-white mb-1">Competition Dates</h3>
                    <p class="text-xs font-semibold" style="color: #b2e0e0;">June 15-21, 2026</p>
                </div>
                <div class="info-card backdrop-blur-md border border-white/20 rounded-2xl p-3 text-center" style="background-color: rgba(182, 216, 141, 0.1);">
                    <div class="info-icon text-xl mb-1">📍</div>
                    <h3 class="text-xs font-bold text-white mb-1">Location</h3>
                    <p class="text-xs font-semibold" style="color: #b6d88d;">Spišská Nová Ves, Slovakia</p>
                </div>
                <div class="info-card backdrop-blur-md border border-white/20 rounded-2xl p-3 text-center" style="background-color: rgba(255, 200, 40, 0.1);">
                    <div class="info-icon text-xl mb-1">🏆</div>
                    <h3 class="text-xs font-bold text-white mb-1">Disciplines</h3>
                    <p class="text-xs font-semibold" style="color: #ffc828;">Accuracy & Style</p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="hero-actions flex flex-col sm:flex-row gap-3 justify-center items-center mb-8">
                <button class="hero-btn primary text-white font-bold py-3 px-6 rounded-2xl shadow-2xl transform hover:-translate-y-2 hover:scale-105 transition-all duration-300 flex items-center gap-2 text-base" style="background-color: #6acef3;" onmouseover="this.style.backgroundColor='#94a3d6'" onmouseout="this.style.backgroundColor='#6acef3'">
                    <span class="btn-icon text-xl">🚀</span>
                    <span>REGISTER NOW</span>
                </button>
                <button class="hero-btn secondary text-white font-bold py-3 px-6 rounded-2xl border-2 transform hover:-translate-y-2 hover:scale-105 transition-all duration-300 flex items-center gap-2 text-base" style="background-color: rgba(180, 92, 77, 0.1); border-color: #b45c4d;" onmouseover="this.style.backgroundColor='rgba(180, 92, 77, 0.2)'" onmouseout="this.style.backgroundColor='rgba(180, 92, 77, 0.1)'">
                    <span class="btn-icon text-xl">📋</span>
                    <span>LEARN MORE</span>
                </button>
            </div>

            <!-- Scroll Indicator -->
            <div class="scroll-indicator absolute bottom-8 left-1/2 transform -translate-x-1/2">
                <div class="scroll-arrow flex flex-col items-center text-white/60 animate-bounce">
                    <span class="text-xs font-medium mb-1">Scroll to explore</span>
                    <div class="w-5 h-8 border-2 border-white/60 rounded-full flex justify-between">
                        <div class="w-1 h-2 bg-white/60 rounded-full mt-1 animate-pulse"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="main-content">
        <!-- How to get here section -->
        <section id="location" class="section py-16 md:py-24" style="background-color: #ffffff;">
            <div class="container mx-auto px-6 max-w-7xl">
                <div class="content-grid grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <div class="text-column space-y-8">
                        <div class="section-header">
                            <h2 class="section-title text-4xl md:text-5xl font-bold mb-4" style="color: #556e78;">How to get here</h2>
                            <p class="section-date text-sm font-medium mb-6 uppercase tracking-wide" style="color: #6acef3;">Travel Information</p>
                            <div class="section-text space-y-6 text-lg leading-relaxed" style="color: #556e78;">
                                <p>
                                    Located in the heart of Slovakia, Spišská Nová Ves offers excellent accessibility for international competitors and spectators. Our venue is strategically positioned with multiple transportation options to ensure your journey to the championship is as smooth as your landing.
                                </p>
                                <p>
                                    Whether you're traveling by car, train, or plane, we've prepared comprehensive travel guides and support services to help you reach the competition site efficiently and comfortably.
                                </p>
                            </div>
                            <button class="cta-button text-white font-bold py-4 px-8 rounded-2xl shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300 flex items-center gap-3 text-lg" style="background-color: #6acef3;" onmouseover="this.style.backgroundColor='#94a3d6'" onmouseout="this.style.backgroundColor='#6acef3'">
                                <span class="button-icon text-xl">📍</span>
                                GET DETAILED DIRECTIONS
                            </button>
                        </div>
                    </div>
                    <div class="image-column flex justify-center">
                        <div class="section-image rounded-3xl overflow-hidden shadow-2xl border-4 border-white transform hover:scale-105 transition-transform duration-300">
                            <img src="images/spiska-nova-ves-aerial.jpg" alt="Aerial view of Spišská Nová Ves airfield" class="section-img w-full h-96 object-cover">
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Disciplines Section -->
        <section id="disciplines" class="disciplines-section py-16 md:py-24" style="background-color: #b6d88d;">
            <div class="container mx-auto px-6 max-w-7xl">
                <div class="section-header text-center mb-16">
                    <h2 class="disciplines-title text-4xl md:text-5xl font-bold mb-4" style="color: #556e78;">Competition Disciplines</h2>
                    <p class="text-xl max-w-3xl mx-auto leading-relaxed" style="color: #556e78;">Two distinct challenges that test the pinnacle of skydiving mastery - precision and artistry combined</p>
                </div>
                <div class="disciplines-content grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div class="discipline-card accuracy bg-white rounded-3xl shadow-xl overflow-hidden hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 border border-gray-100">
                        <div class="discipline-image-wrapper relative overflow-hidden">
                            <img src="images/accuracy-landing.jpg" alt="Skydiver performing accuracy landing" class="discipline-bg object-cover h-64 w-full hover:scale-110 transition-transform duration-500">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                        </div>
                        <div class="discipline-overlay p-8">
                            <div class="flex items-center gap-3 mb-4">
                                <div class="icon text-white p-3 rounded-xl shadow-lg" style="background-color: #b45c4d;">
                                    <span class="text-2xl">🎯</span>
                                </div>
                                <h3 class="text-2xl font-bold" style="color: #feffff;">Accuracy Landing</h3>
                            </div>
                            <p class="text-lg leading-relaxed mb-6" style="color: #ffffff;">Precision is everything. Competitors aim for a target with millimeter accuracy, testing their skill and control in the ultimate landing challenge where every centimeter counts.</p>
                            <button class="learn-more-btn text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg" style="background-color: #b45c4d;" onmouseover="this.style.backgroundColor='#94a3d6'" onmouseout="this.style.backgroundColor='#b45c4d'">
                                Learn More About Accuracy
                            </button>
                        </div>
                    </div>
                    <div class="discipline-card style bg-white rounded-3xl shadow-xl overflow-hidden hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 border border-gray-100">
                        <div class="discipline-image-wrapper relative overflow-hidden">
                            <img src="images/individual-style.jpg" alt="Skydiver performing aerial maneuvers" class="discipline-bg object-cover h-64 w-full hover:scale-110 transition-transform duration-500">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                        </div>
                        <div class="discipline-overlay p-8">
                            <div class="flex items-center gap-3 mb-4">
                                <div class="icon text-white p-3 rounded-xl shadow-lg" style="background-color: #94a3d6;">
                                    <span class="text-2xl">🎨</span>
                                </div>
                                <h3 class="text-2xl font-bold" style="color: #ffffff;">Individual Style</h3>
                            </div>
                            <p class="text-lg leading-relaxed mb-6" style="color: #ffffff;">Artistry meets athleticism. Skydivers perform complex aerial maneuvers, showcasing grace, technique, and perfect execution in a stunning display of aerial ballet.</p>
                            <button class="learn-more-btn text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg" style="background-color: #94a3d6;" onmouseover="this.style.backgroundColor='#6acef3'" onmouseout="this.style.backgroundColor='#94a3d6'">
                                Learn More About Style
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Accommodation section -->
        <section id="accommodation" class="section accommodation py-16 md:py-24" style="background-color: #ffc828;">
            <div class="container mx-auto px-6 max-w-7xl">
                <div class="section-header text-center mb-16">
                    <h2 class="section-title text-4xl md:text-5xl font-bold mb-4" style="color: #556e78;">Accommodation</h2>
                    <p class="text-xl max-w-3xl mx-auto leading-relaxed" style="color: #556e78;">Comfortable stays near the championship venue with convenient access to the competition site</p>
                </div>
                <div class="hotels-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <div class="hotel-card bg-white rounded-3xl shadow-xl overflow-hidden hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 border border-gray-100">
                        <div class="hotel-image relative overflow-hidden">
                            <img src="images/hotel-metropol.jpg" alt="Hotel Metropol exterior" class="hotel-img object-cover h-48 w-full hover:scale-110 transition-transform duration-300">
                            <div class="absolute top-4 right-4 text-white px-3 py-1 rounded-full text-sm font-semibold" style="background-color: #6acef3;">
                                ⭐ Premium
                            </div>
                        </div>
                        <div class="hotel-info p-6">
                            <h3 class="hotel-name text-xl font-bold mb-2" style="color: #556e78;">HOTEL METROPOL</h3>
                            <p class="hotel-distance text-gray-500 mb-4 flex items-center gap-2">
                                <span style="color: #6acef3;">📍</span>
                                2 km from airfield
                            </p>
                            <button class="w-full text-white font-semibold py-3 px-4 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg" style="background-color: #6acef3;" onmouseover="this.style.backgroundColor='#94a3d6'" onmouseout="this.style.backgroundColor='#6acef3'">
                                Book Now
                            </button>
                        </div>
                    </div>
                    <div class="hotel-card bg-white rounded-3xl shadow-xl overflow-hidden hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 border border-gray-100">
                        <div class="hotel-image relative overflow-hidden">
                            <img src="images/park-hotel.jpg" alt="Park Hotel exterior" class="hotel-img object-cover h-48 w-full hover:scale-110 transition-transform duration-300">
                            <div class="absolute top-4 right-4 text-white px-3 py-1 rounded-full text-sm font-semibold" style="background-color: #b6d88d;">
                                🌟 Closest
                            </div>
                        </div>
                        <div class="hotel-info p-6">
                            <h3 class="hotel-name text-xl font-bold mb-2" style="color: #556e78;">PARK HOTEL</h3>
                            <p class="hotel-distance text-gray-500 mb-4 flex items-center gap-2">
                                <span style="color: #b6d88d;">📍</span>
                                1 km from airfield
                            </p>
                            <button class="w-full text-white font-semibold py-3 px-4 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg" style="background-color: #b6d88d;" onmouseover="this.style.backgroundColor='#6acef3'" onmouseout="this.style.backgroundColor='#b6d88d'">
                                Book Now
                            </button>
                        </div>
                    </div>
                    <div class="hotel-card bg-white rounded-3xl shadow-xl overflow-hidden hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 border border-gray-100">
                        <div class="hotel-image relative overflow-hidden">
                            <img src="images/hotel-metropol-2.jpg" alt="Hotel Metropol room" class="hotel-img object-cover h-48 w-full hover:scale-110 transition-transform duration-300">
                            <div class="absolute top-4 right-4 text-white px-3 py-1 rounded-full text-sm font-semibold" style="background-color: #94a3d6;">
                                💎 Suite
                            </div>
                        </div>
                        <div class="hotel-info p-6">
                            <h3 class="hotel-name text-xl font-bold mb-2" style="color: #556e78;">METROPOL SUITE</h3>
                            <p class="hotel-distance text-gray-500 mb-4 flex items-center gap-2">
                                <span style="color: #94a3d6;">📍</span>
                                2 km from airfield
                            </p>
                            <button class="w-full text-white font-semibold py-3 px-4 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg" style="background-color: #94a3d6;" onmouseover="this.style.backgroundColor='#b45c4d'" onmouseout="this.style.backgroundColor='#94a3d6'">
                                Book Now
                            </button>
                        </div>
                    </div>
                    <div class="hotel-card bg-white rounded-3xl shadow-xl overflow-hidden hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 border border-gray-100">
                        <div class="hotel-image relative overflow-hidden">
                            <img src="images/park-hotel-2.jpg" alt="Park Hotel room" class="hotel-img object-cover h-48 w-full hover:scale-110 transition-transform duration-300">
                            <div class="absolute top-4 right-4 text-white px-3 py-1 rounded-full text-sm font-semibold" style="background-color: #b45c4d;">
                                🏆 Deluxe
                            </div>
                        </div>
                        <div class="hotel-info p-6">
                            <h3 class="hotel-name text-xl font-bold mb-2" style="color: #556e78;">PARK DELUXE</h3>
                            <p class="hotel-distance text-gray-500 mb-4 flex items-center gap-2">
                                <span style="color: #b45c4d;">📍</span>
                                1 km from airfield
                            </p>
                            <button class="w-full text-white font-semibold py-3 px-4 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg" style="background-color: #b45c4d;" onmouseover="this.style.backgroundColor='#ffc828'" onmouseout="this.style.backgroundColor='#b45c4d'">
                                Book Now
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Gallery/Carousel Section -->
        <section id="gallery" class="gallery py-16 md:py-24" style="background-color: #94a3d6;">
            <div class="container mx-auto px-6 max-w-7xl">
                <div class="section-header text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-bold mb-4" style="color: #556e78;">Championship Gallery</h2>
                    <p class="text-xl max-w-3xl mx-auto leading-relaxed" style="color: #556e78;">Immerse yourself in the breathtaking world of competitive skydiving and the stunning Slovak landscape</p>
                </div>
                <div class="carousel grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div class="carousel-item main-item rounded-3xl shadow-2xl overflow-hidden hover:shadow-3xl transform hover:-translate-y-2 transition-all duration-300 border border-gray-100 relative">
                        <div class="relative overflow-hidden h-full">
                            <img src="images/championship-gallery-1.jpg" alt="Championship action shots" class="carousel-img object-cover h-full w-full hover:scale-110 transition-transform duration-500">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-black/20"></div>
                            <div class="absolute top-6 left-6 text-white px-4 py-2 rounded-xl font-semibold shadow-lg" style="background-color: #b45c4d;">
                                🏆 Competition
                            </div>
                            <div class="absolute bottom-0 left-0 right-0 p-8 text-white">
                                <h3 class="text-2xl font-bold mb-3 text-white">Championship Highlights</h3>
                                <p class="text-lg leading-relaxed mb-6 text-white">Witness the world's best skydivers in action as they compete for precision and style in the ultimate aerial competition.</p>
                                <button class="text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg" style="background-color: #b45c4d;" onmouseover="this.style.backgroundColor='#6acef3'" onmouseout="this.style.backgroundColor='#b45c4d'">
                                    View Photo Gallery
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="carousel-item side-item rounded-3xl shadow-2xl overflow-hidden hover:shadow-3xl transform hover:-translate-y-2 transition-all duration-300 border border-gray-100 relative">
                        <div class="relative overflow-hidden h-full">
                            <img src="images/spiska-nova-ves-landscape.jpg" alt="Spišská Nová Ves landscape" class="carousel-img object-cover h-full w-full hover:scale-110 transition-transform duration-500">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-black/20"></div>
                            <div class="absolute top-6 left-6 text-white px-4 py-2 rounded-xl font-semibold shadow-lg" style="background-color: #b6d88d;">
                                🏔️ Location
                            </div>
                            <div class="absolute bottom-0 left-0 right-0 p-8 text-white">
                                <h3 class="text-2xl font-bold mb-3 text-white">Stunning Location</h3>
                                <p class="text-lg leading-relaxed mb-6 text-white">Experience the natural beauty of Spišská Nová Ves, where pristine landscapes meet world-class sporting excellence.</p>
                                <button class="text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg" style="background-color: #b6d88d;" onmouseover="this.style.backgroundColor='#ffc828'" onmouseout="this.style.backgroundColor='#b6d88d'">
                                    Explore Venue
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer py-16 text-white relative overflow-hidden" style="background-color: #556e78;">
        <div class="container mx-auto px-6 max-w-7xl relative z-10">
            <div class="footer-content text-center space-y-8">
                <div class="footer-header mb-12">
                    <h3 class="text-3xl font-bold text-white mb-4">World Skydiving Championship 2026</h3>
                    <p class="text-xl max-w-2xl mx-auto leading-relaxed" style="color: #b2e0e0;">Join us in Spišská Nová Ves for the ultimate celebration of skydiving excellence</p>
                </div>
                
                <div class="footer-info grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                    <div class="info-block">
                        <div class="icon w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-xl" style="background-color: #6acef3;">
                            <span class="text-2xl">📅</span>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">Competition Dates</h4>
                        <p style="color: #b2e0e0;">June 15-21, 2026</p>
                    </div>
                    <div class="info-block">
                        <div class="icon w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-xl" style="background-color: #b6d88d;">
                            <span class="text-2xl">📍</span>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">Location</h4>
                        <p style="color: #b2e0e0;">Spišská Nová Ves, Slovakia</p>
                    </div>
                    <div class="info-block">
                        <div class="icon w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-xl" style="background-color: #ffc828;">
                            <span class="text-2xl">🏆</span>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">Disciplines</h4>
                        <p style="color: #b2e0e0;">Accuracy Landing & Individual Style</p>
                    </div>
                </div>

                <div class="footer-logos flex justify-center gap-8 mb-8">
                    <div class="logo-item w-16 h-16 rounded-2xl shadow-xl hover:shadow-2xl hover:scale-110 transition-all duration-300 flex items-center justify-center" style="background-color: #94a3d6;">
                        <span class="text-2xl">🪂</span>
                    </div>
                    <div class="logo-item w-16 h-16 rounded-2xl shadow-xl hover:shadow-2xl hover:scale-110 transition-all duration-300 flex items-center justify-center" style="background-color: #b45c4d;">
                        <span class="text-2xl">🌍</span>
                    </div>
                    <div class="logo-item w-16 h-16 rounded-2xl shadow-xl hover:shadow-2xl hover:scale-110 transition-all duration-300 flex items-center justify-center" style="background-color: #6acef3;">
                        <span class="text-2xl">🇸🇰</span>
                    </div>
                </div>
                
                <div class="footer-bottom pt-8" style="border-top: 1px solid #b2e0e0;">
                    <p class="footer-text text-lg" style="color: #b2e0e0;">
                        © 2026 World Skydiving Championship | Organized with passion for aerial excellence
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
