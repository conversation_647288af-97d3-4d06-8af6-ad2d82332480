<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>World Skydiving Championship 2026 - Spišská Nová Ves</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'sky-calm': '#b2e0e0',
                        'grass-green': '#b6d88d',
                        'landing-clay': '#b45c4d',
                        'freefall-blue': '#6acef3',
                        'parachute-mist': '#94a3d6',
                        'target-yellow': '#ffc828',
                        'steel-flight': '#556e78',
                    },
                    fontFamily: {
                        'primary': ['Poppins', 'sans-serif'],
                        'secondary': ['Inter', 'sans-serif'],
                    },
                    animation: {
                        'float-gentle': 'float-gentle 8s ease-in-out infinite',
                        'hero-fade-in': 'hero-fade-in 1.2s ease-out forwards',
                        'ripple': 'ripple 0.6s ease-out',
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>

    <!-- Navigation with Tailwind -->
    <nav class="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-xl border-b border-white/20 transition-all duration-300" role="navigation" aria-label="Main navigation" id="main-nav">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-20">
                <!-- Logo/Brand -->
                <div class="flex items-center gap-4 z-50">
                    <div class="w-12 h-12 bg-gradient-to-br from-freefall-blue to-parachute-mist rounded-xl flex items-center justify-center shadow-lg hover:scale-105 transition-transform duration-300">
                        <span class="text-xl" aria-hidden="true">🪂</span>
                    </div>
                    <div class="hidden sm:flex items-center gap-3">
                        <h1 class="text-xl font-bold text-steel-flight">World Championship</h1>
                        <span class="text-sky-calm font-light" aria-hidden="true">•</span>
                        <p class="text-sm font-medium text-parachute-mist">Skydiving 2026</p>
                    </div>
                </div>

                <!-- Navigation Links -->
                <div class="hidden md:flex items-center gap-2" id="nav-links">
                    <a href="#home" class="nav-link flex items-center gap-2 px-4 py-2 rounded-xl text-steel-flight font-medium transition-all duration-300 hover:bg-freefall-blue/10 hover:text-freefall-blue active" data-section="home">
                        <span class="text-base" aria-hidden="true">🏠</span>
                        <span>Home</span>
                    </a>
                    <a href="#disciplines" class="nav-link flex items-center gap-2 px-4 py-2 rounded-xl text-steel-flight font-medium transition-all duration-300 hover:bg-freefall-blue/10 hover:text-freefall-blue" data-section="disciplines">
                        <span class="text-base" aria-hidden="true">🎯</span>
                        <span>Disciplines</span>
                    </a>
                    <a href="#location" class="nav-link flex items-center gap-2 px-4 py-2 rounded-xl text-steel-flight font-medium transition-all duration-300 hover:bg-freefall-blue/10 hover:text-freefall-blue" data-section="location">
                        <span class="text-base" aria-hidden="true">📍</span>
                        <span>Location</span>
                    </a>
                    <a href="#accommodation" class="nav-link flex items-center gap-2 px-4 py-2 rounded-xl text-steel-flight font-medium transition-all duration-300 hover:bg-freefall-blue/10 hover:text-freefall-blue" data-section="accommodation">
                        <span class="text-base" aria-hidden="true">🏨</span>
                        <span>Hotels</span>
                    </a>
                    <a href="#gallery" class="nav-link flex items-center gap-2 px-4 py-2 rounded-xl text-steel-flight font-medium transition-all duration-300 hover:bg-freefall-blue/10 hover:text-freefall-blue" data-section="gallery">
                        <span class="text-base" aria-hidden="true">📸</span>
                        <span>Gallery</span>
                    </a>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center gap-3">
                    <button class="w-11 h-11 rounded-xl bg-steel-flight/10 flex items-center justify-center hover:bg-steel-flight/20 transition-all duration-300 theme-toggle" aria-label="Toggle dark mode" title="Toggle theme">
                        <span class="text-xl theme-icon light-icon" aria-hidden="true">☀️</span>
                        <span class="text-xl theme-icon dark-icon opacity-0 absolute" aria-hidden="true">🌙</span>
                    </button>
                    <button class="hidden sm:flex items-center gap-2 px-4 py-2 rounded-xl bg-steel-flight/10 text-steel-flight font-medium hover:bg-steel-flight/20 transition-all duration-300" aria-label="More information">
                        <span class="text-lg" aria-hidden="true">ℹ️</span>
                        <span>Info</span>
                    </button>
                    <button class="flex items-center gap-2 px-6 py-3 rounded-xl bg-gradient-to-r from-freefall-blue to-parachute-mist text-white font-bold shadow-lg hover:shadow-xl hover:-translate-y-0.5 transition-all duration-300" aria-label="Register for championship">
                        <span class="text-lg" aria-hidden="true">📝</span>
                        <span>Register</span>
                    </button>
                    <button class="md:hidden flex flex-col justify-center items-center w-11 h-11 gap-1 mobile-menu-toggle" aria-label="Toggle mobile menu" aria-expanded="false" aria-controls="mobile-nav">
                        <span class="w-6 h-0.5 bg-steel-flight rounded-full transition-all duration-300 hamburger-line"></span>
                        <span class="w-6 h-0.5 bg-steel-flight rounded-full transition-all duration-300 hamburger-line"></span>
                        <span class="w-6 h-0.5 bg-steel-flight rounded-full transition-all duration-300 hamburger-line"></span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div class="md:hidden fixed top-20 left-0 right-0 bg-white/98 backdrop-blur-xl shadow-xl transform -translate-y-full opacity-0 invisible transition-all duration-300 mobile-nav" id="mobile-nav">
            <div class="px-4 py-6 space-y-4">
                <a href="#home" class="nav-link flex items-center gap-3 px-4 py-3 rounded-xl text-steel-flight font-medium hover:bg-freefall-blue/10 hover:text-freefall-blue transition-all duration-300" data-section="home">
                    <span class="text-xl" aria-hidden="true">🏠</span>
                    <span>Home</span>
                </a>
                <a href="#disciplines" class="nav-link flex items-center gap-3 px-4 py-3 rounded-xl text-steel-flight font-medium hover:bg-freefall-blue/10 hover:text-freefall-blue transition-all duration-300" data-section="disciplines">
                    <span class="text-xl" aria-hidden="true">🎯</span>
                    <span>Disciplines</span>
                </a>
                <a href="#location" class="nav-link flex items-center gap-3 px-4 py-3 rounded-xl text-steel-flight font-medium hover:bg-freefall-blue/10 hover:text-freefall-blue transition-all duration-300" data-section="location">
                    <span class="text-xl" aria-hidden="true">📍</span>
                    <span>Location</span>
                </a>
                <a href="#accommodation" class="nav-link flex items-center gap-3 px-4 py-3 rounded-xl text-steel-flight font-medium hover:bg-freefall-blue/10 hover:text-freefall-blue transition-all duration-300" data-section="accommodation">
                    <span class="text-xl" aria-hidden="true">🏨</span>
                    <span>Hotels</span>
                </a>
                <a href="#gallery" class="nav-link flex items-center gap-3 px-4 py-3 rounded-xl text-steel-flight font-medium hover:bg-freefall-blue/10 hover:text-freefall-blue transition-all duration-300" data-section="gallery">
                    <span class="text-xl" aria-hidden="true">📸</span>
                    <span>Gallery</span>
                </a>
            </div>
        </div>

        <!-- Scroll Progress Indicator -->
        <div class="absolute bottom-0 left-0 h-1 bg-gradient-to-r from-freefall-blue via-parachute-mist to-target-yellow transform scale-x-0 origin-left transition-transform duration-100 scroll-progress" aria-hidden="true"></div>
    </nav>

    <!-- Hero Section with Tailwind -->
    <section id="home" class="relative min-h-screen flex items-center justify-center overflow-hidden pt-20">
        <!-- Background Image -->
        <div class="absolute inset-0 z-0">
            <img src="images/sky-clouds-background.jpg"
                 alt="Sky with clouds background"
                 class="w-full h-full object-cover opacity-80"
                 loading="eager">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-900/40 via-gray-900/30 to-black/50"></div>
        </div>

        <!-- Floating Background Elements -->
        <div class="absolute inset-0 z-10 pointer-events-none" aria-hidden="true">
            <div class="absolute top-1/4 left-1/4 w-32 h-32 bg-freefall-blue/10 rounded-full animate-float-gentle"></div>
            <div class="absolute top-3/4 right-1/4 w-24 h-24 bg-parachute-mist/10 rounded-full animate-float-gentle" style="animation-delay: 2s;"></div>
            <div class="absolute bottom-1/4 left-1/3 w-28 h-28 bg-target-yellow/10 rounded-full animate-float-gentle" style="animation-delay: 4s;"></div>
        </div>

        <!-- Hero Content -->
        <div class="relative z-20 text-center max-w-6xl mx-auto px-6 py-12">
            <!-- Logo and Title Section -->
            <div class="flex flex-col lg:flex-row items-center justify-center gap-8 lg:gap-12 mb-8">
                <!-- Championship Logo -->
                <div class="relative">
                    <div class="absolute inset-0 bg-gradient-to-r from-freefall-blue/30 to-parachute-mist/30 rounded-full blur-xl animate-pulse"></div>
                    <div class="relative w-32 h-32 lg:w-40 lg:h-40 bg-white/10 backdrop-blur-md border border-white/20 rounded-3xl p-4 shadow-2xl hover:scale-105 transition-transform duration-300">
                        <img src="images/logo.jpg"
                             alt="World Skydiving Championship 2026 Logo"
                             class="w-full h-full object-contain rounded-2xl shadow-xl"
                             loading="eager">
                    </div>
                </div>

                <!-- Main Title -->
                <div class="text-center lg:text-left">
                    <h1 class="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black leading-tight mb-4">
                        <span class="block text-sky-calm animate-hero-fade-in">WORLD</span>
                        <span class="block text-white animate-hero-fade-in" style="animation-delay: 0.2s;">SKYDIVING</span>
                        <span class="block text-target-yellow animate-hero-fade-in" style="animation-delay: 0.4s;">CHAMPIONSHIP</span>
                    </h1>
                    <div class="text-2xl lg:text-3xl font-bold text-parachute-mist animate-hero-fade-in" style="animation-delay: 0.6s;">2026</div>
                </div>
            </div>

            <!-- Description -->
            <p class="text-lg md:text-xl text-gray-200 max-w-4xl mx-auto leading-relaxed mb-8 animate-hero-fade-in" style="animation-delay: 0.8s;">
                Witness the ultimate test of precision and artistry as the world's elite skydivers compete for glory in the heart of Slovakia. Every jump, every landing, every movement defines champions.
            </p>

            <!-- Info Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 max-w-4xl mx-auto">
                <div class="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6 text-center hover:bg-white/15 transition-all duration-300 animate-hero-fade-in" style="animation-delay: 1s;">
                    <div class="text-3xl mb-3">📅</div>
                    <h3 class="text-sm font-bold text-white mb-2 uppercase tracking-wide">Competition Dates</h3>
                    <p class="text-lg font-semibold text-sky-calm">June 15-21, 2026</p>
                </div>
                <div class="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6 text-center hover:bg-white/15 transition-all duration-300 animate-hero-fade-in" style="animation-delay: 1.2s;">
                    <div class="text-3xl mb-3">📍</div>
                    <h3 class="text-sm font-bold text-white mb-2 uppercase tracking-wide">Location</h3>
                    <p class="text-lg font-semibold text-grass-green">Spišská Nová Ves, Slovakia</p>
                </div>
                <div class="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6 text-center hover:bg-white/15 transition-all duration-300 animate-hero-fade-in" style="animation-delay: 1.4s;">
                    <div class="text-3xl mb-3">🏆</div>
                    <h3 class="text-sm font-bold text-white mb-2 uppercase tracking-wide">Disciplines</h3>
                    <p class="text-lg font-semibold text-target-yellow">Accuracy & Style</p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
                <button class="group relative overflow-hidden bg-gradient-to-r from-freefall-blue to-parachute-mist text-white font-bold py-4 px-8 rounded-2xl shadow-2xl hover:shadow-3xl transform hover:-translate-y-2 hover:scale-105 transition-all duration-300 flex items-center gap-3 text-lg animate-hero-fade-in" style="animation-delay: 1.6s;" data-action="register">
                    <span class="text-2xl group-hover:scale-110 transition-transform duration-300">🚀</span>
                    <span>REGISTER NOW</span>
                    <div class="absolute inset-0 bg-white/20 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                </button>
                <button class="group relative overflow-hidden bg-landing-clay/20 border-2 border-landing-clay text-white font-bold py-4 px-8 rounded-2xl hover:bg-landing-clay/30 transform hover:-translate-y-2 hover:scale-105 transition-all duration-300 flex items-center gap-3 text-lg animate-hero-fade-in" style="animation-delay: 1.8s;" data-action="learn-more">
                    <span class="text-2xl group-hover:scale-110 transition-transform duration-300">📋</span>
                    <span>LEARN MORE</span>
                </button>
            </div>

            <!-- Scroll Indicator -->
            <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-hero-fade-in" style="animation-delay: 2s;">
                <div class="flex flex-col items-center text-white/70 animate-bounce">
                    <span class="text-sm font-medium mb-2 uppercase tracking-wide">Scroll to explore</span>
                    <div class="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
                        <div class="w-1 h-3 bg-white/70 rounded-full mt-2 animate-pulse"></div>
                    </div>
                    <svg class="w-6 h-6 mt-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                    </svg>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="main-content">
        <!-- How to get here section -->
        <section id="location" class="section py-16 md:py-24" style="background-color: #ffffff;">
            <div class="container mx-auto px-6 max-w-7xl">
                <div class="content-grid grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <div class="text-column space-y-8">
                        <div class="section-header">
                            <h2 class="section-title text-4xl md:text-5xl font-bold mb-4" style="color: #556e78;">How to get here</h2>
                            <p class="section-date text-sm font-medium mb-6 uppercase tracking-wide" style="color: #6acef3;">Travel Information</p>
                            <div class="section-text space-y-6 text-lg leading-relaxed" style="color: #556e78;">
                                <p>
                                    Located in the heart of Slovakia, Spišská Nová Ves offers excellent accessibility for international competitors and spectators. Our venue is strategically positioned with multiple transportation options to ensure your journey to the championship is as smooth as your landing.
                                </p>
                                <p>
                                    Whether you're traveling by car, train, or plane, we've prepared comprehensive travel guides and support services to help you reach the competition site efficiently and comfortably.
                                </p>
                            </div>
                            <button class="cta-button text-white font-bold py-4 px-8 rounded-2xl shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300 flex items-center gap-3 text-lg" style="background-color: #6acef3;" onmouseover="this.style.backgroundColor='#94a3d6'" onmouseout="this.style.backgroundColor='#6acef3'">
                                <span class="button-icon text-xl">📍</span>
                                GET DETAILED DIRECTIONS
                            </button>
                        </div>
                    </div>
                    <div class="image-column flex justify-center">
                        <div class="section-image rounded-3xl overflow-hidden shadow-2xl border-4 border-white transform hover:scale-105 transition-transform duration-300">
                            <img src="images/spiska-nova-ves-aerial.jpg" alt="Aerial view of Spišská Nová Ves airfield" class="section-img w-full h-96 object-cover">
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Disciplines Section with Tailwind -->
        <section id="disciplines" class="py-20 bg-grass-green relative overflow-hidden">
            <!-- Background Gradient -->
            <div class="absolute inset-0 bg-gradient-to-br from-grass-green via-grass-green/90 to-parachute-mist/20"></div>

            <div class="relative z-10 max-w-7xl mx-auto px-6">
                <!-- Section Header -->
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl lg:text-6xl font-black text-steel-flight mb-6 relative inline-block">
                        Competition Disciplines
                        <div class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-gradient-to-r from-freefall-blue to-parachute-mist rounded-full"></div>
                    </h2>
                    <p class="text-xl text-steel-flight/90 max-w-3xl mx-auto leading-relaxed">
                        Two distinct challenges that test the pinnacle of skydiving mastery - precision and artistry combined
                    </p>
                </div>

                <!-- Disciplines Grid -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
                    <!-- Accuracy Landing Card -->
                    <article class="group relative bg-white rounded-3xl overflow-hidden shadow-2xl hover:shadow-3xl transform hover:-translate-y-3 hover:scale-105 transition-all duration-500 cursor-pointer h-[500px]" data-discipline="accuracy">
                        <!-- Background Image -->
                        <div class="absolute inset-0">
                            <img src="images/accuracy-landing.jpg"
                                 alt="Skydiver performing accuracy landing"
                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                                 loading="lazy">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-black/20"></div>
                        </div>

                        <!-- Content -->
                        <div class="relative z-10 h-full flex flex-col justify-end p-8 text-white">
                            <!-- Header -->
                            <div class="flex items-center gap-4 mb-6">
                                <div class="w-16 h-16 bg-gradient-to-br from-landing-clay to-target-yellow rounded-2xl flex items-center justify-center text-2xl shadow-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300">
                                    <span aria-hidden="true">🎯</span>
                                </div>
                                <h3 class="text-3xl font-bold">Accuracy Landing</h3>
                            </div>

                            <!-- Description -->
                            <p class="text-lg leading-relaxed mb-6 opacity-90">
                                Precision is everything. Competitors aim for a target with millimeter accuracy, testing their skill and control in the ultimate landing challenge where every centimeter counts.
                            </p>

                            <!-- Stats -->
                            <div class="flex gap-4 mb-6">
                                <div class="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl px-4 py-3 text-center group-hover:-translate-y-1 transition-transform duration-300">
                                    <div class="text-xl font-bold text-target-yellow">±2cm</div>
                                    <div class="text-xs uppercase tracking-wide opacity-80">Target Precision</div>
                                </div>
                                <div class="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl px-4 py-3 text-center group-hover:-translate-y-1 transition-transform duration-300" style="transition-delay: 0.1s;">
                                    <div class="text-xl font-bold text-target-yellow">10</div>
                                    <div class="text-xs uppercase tracking-wide opacity-80">Rounds</div>
                                </div>
                            </div>

                            <!-- Button -->
                            <button class="flex items-center justify-between bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl px-6 py-4 hover:bg-white/20 group-hover:-translate-y-1 transition-all duration-300" data-action="learn-accuracy">
                                <span class="font-semibold">Learn More About Accuracy</span>
                                <span class="text-xl group-hover:translate-x-2 transition-transform duration-300" aria-hidden="true">→</span>
                            </button>
                        </div>

                        <!-- Glow Effect -->
                        <div class="absolute inset-0 bg-gradient-to-r from-freefall-blue/20 to-parachute-mist/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
                    </article>

                    <!-- Individual Style Card -->
                    <article class="group relative bg-white rounded-3xl overflow-hidden shadow-2xl hover:shadow-3xl transform hover:-translate-y-3 hover:scale-105 transition-all duration-500 cursor-pointer h-[500px]" data-discipline="style">
                        <!-- Background Image -->
                        <div class="absolute inset-0">
                            <img src="images/individual-style.jpg"
                                 alt="Skydiver performing aerial maneuvers"
                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                                 loading="lazy">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-black/20"></div>
                        </div>

                        <!-- Content -->
                        <div class="relative z-10 h-full flex flex-col justify-end p-8 text-white">
                            <!-- Header -->
                            <div class="flex items-center gap-4 mb-6">
                                <div class="w-16 h-16 bg-gradient-to-br from-parachute-mist to-freefall-blue rounded-2xl flex items-center justify-center text-2xl shadow-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300">
                                    <span aria-hidden="true">🎨</span>
                                </div>
                                <h3 class="text-3xl font-bold">Individual Style</h3>
                            </div>

                            <!-- Description -->
                            <p class="text-lg leading-relaxed mb-6 opacity-90">
                                Artistry meets athleticism. Skydivers perform complex aerial maneuvers, showcasing grace, technique, and perfect execution in a stunning display of aerial ballet.
                            </p>

                            <!-- Stats -->
                            <div class="flex gap-4 mb-6">
                                <div class="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl px-4 py-3 text-center group-hover:-translate-y-1 transition-transform duration-300">
                                    <div class="text-xl font-bold text-freefall-blue">4</div>
                                    <div class="text-xs uppercase tracking-wide opacity-80">Maneuvers</div>
                                </div>
                                <div class="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl px-4 py-3 text-center group-hover:-translate-y-1 transition-transform duration-300" style="transition-delay: 0.1s;">
                                    <div class="text-xl font-bold text-freefall-blue">8</div>
                                    <div class="text-xs uppercase tracking-wide opacity-80">Rounds</div>
                                </div>
                            </div>

                            <!-- Button -->
                            <button class="flex items-center justify-between bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl px-6 py-4 hover:bg-white/20 group-hover:-translate-y-1 transition-all duration-300" data-action="learn-style">
                                <span class="font-semibold">Learn More About Style</span>
                                <span class="text-xl group-hover:translate-x-2 transition-transform duration-300" aria-hidden="true">→</span>
                            </button>
                        </div>

                        <!-- Glow Effect -->
                        <div class="absolute inset-0 bg-gradient-to-r from-freefall-blue/20 to-parachute-mist/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
                    </article>
                </div>
            </div>
        </section>

        <!-- Accommodation section -->
        <section id="accommodation" class="section accommodation py-16 md:py-24" style="background-color: #ffc828;">
            <div class="container mx-auto px-6 max-w-7xl">
                <div class="section-header text-center mb-16">
                    <h2 class="section-title text-4xl md:text-5xl font-bold mb-4" style="color: #556e78;">Accommodation</h2>
                    <p class="text-xl max-w-3xl mx-auto leading-relaxed" style="color: #556e78;">Comfortable stays near the championship venue with convenient access to the competition site</p>
                </div>
                <div class="hotels-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <div class="hotel-card bg-white rounded-3xl shadow-xl overflow-hidden hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 border border-gray-100">
                        <div class="hotel-image relative overflow-hidden">
                            <img src="images/hotel-metropol.jpg" alt="Hotel Metropol exterior" class="hotel-img object-cover h-48 w-full hover:scale-110 transition-transform duration-300">
                            <div class="absolute top-4 right-4 text-white px-3 py-1 rounded-full text-sm font-semibold" style="background-color: #6acef3;">
                                ⭐ Premium
                            </div>
                        </div>
                        <div class="hotel-info p-6">
                            <h3 class="hotel-name text-xl font-bold mb-2" style="color: #556e78;">HOTEL METROPOL</h3>
                            <p class="hotel-distance text-gray-500 mb-4 flex items-center gap-2">
                                <span style="color: #6acef3;">📍</span>
                                2 km from airfield
                            </p>
                            <button class="w-full text-white font-semibold py-3 px-4 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg" style="background-color: #6acef3;" onmouseover="this.style.backgroundColor='#94a3d6'" onmouseout="this.style.backgroundColor='#6acef3'">
                                Book Now
                            </button>
                        </div>
                    </div>
                    <div class="hotel-card bg-white rounded-3xl shadow-xl overflow-hidden hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 border border-gray-100">
                        <div class="hotel-image relative overflow-hidden">
                            <img src="images/park-hotel.jpg" alt="Park Hotel exterior" class="hotel-img object-cover h-48 w-full hover:scale-110 transition-transform duration-300">
                            <div class="absolute top-4 right-4 text-white px-3 py-1 rounded-full text-sm font-semibold" style="background-color: #b6d88d;">
                                🌟 Closest
                            </div>
                        </div>
                        <div class="hotel-info p-6">
                            <h3 class="hotel-name text-xl font-bold mb-2" style="color: #556e78;">PARK HOTEL</h3>
                            <p class="hotel-distance text-gray-500 mb-4 flex items-center gap-2">
                                <span style="color: #b6d88d;">📍</span>
                                1 km from airfield
                            </p>
                            <button class="w-full text-white font-semibold py-3 px-4 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg" style="background-color: #b6d88d;" onmouseover="this.style.backgroundColor='#6acef3'" onmouseout="this.style.backgroundColor='#b6d88d'">
                                Book Now
                            </button>
                        </div>
                    </div>
                    <div class="hotel-card bg-white rounded-3xl shadow-xl overflow-hidden hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 border border-gray-100">
                        <div class="hotel-image relative overflow-hidden">
                            <img src="images/hotel-metropol-2.jpg" alt="Hotel Metropol room" class="hotel-img object-cover h-48 w-full hover:scale-110 transition-transform duration-300">
                            <div class="absolute top-4 right-4 text-white px-3 py-1 rounded-full text-sm font-semibold" style="background-color: #94a3d6;">
                                💎 Suite
                            </div>
                        </div>
                        <div class="hotel-info p-6">
                            <h3 class="hotel-name text-xl font-bold mb-2" style="color: #556e78;">METROPOL SUITE</h3>
                            <p class="hotel-distance text-gray-500 mb-4 flex items-center gap-2">
                                <span style="color: #94a3d6;">📍</span>
                                2 km from airfield
                            </p>
                            <button class="w-full text-white font-semibold py-3 px-4 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg" style="background-color: #94a3d6;" onmouseover="this.style.backgroundColor='#b45c4d'" onmouseout="this.style.backgroundColor='#94a3d6'">
                                Book Now
                            </button>
                        </div>
                    </div>
                    <div class="hotel-card bg-white rounded-3xl shadow-xl overflow-hidden hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 border border-gray-100">
                        <div class="hotel-image relative overflow-hidden">
                            <img src="images/park-hotel-2.jpg" alt="Park Hotel room" class="hotel-img object-cover h-48 w-full hover:scale-110 transition-transform duration-300">
                            <div class="absolute top-4 right-4 text-white px-3 py-1 rounded-full text-sm font-semibold" style="background-color: #b45c4d;">
                                🏆 Deluxe
                            </div>
                        </div>
                        <div class="hotel-info p-6">
                            <h3 class="hotel-name text-xl font-bold mb-2" style="color: #556e78;">PARK DELUXE</h3>
                            <p class="hotel-distance text-gray-500 mb-4 flex items-center gap-2">
                                <span style="color: #b45c4d;">📍</span>
                                1 km from airfield
                            </p>
                            <button class="w-full text-white font-semibold py-3 px-4 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg" style="background-color: #b45c4d;" onmouseover="this.style.backgroundColor='#ffc828'" onmouseout="this.style.backgroundColor='#b45c4d'">
                                Book Now
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Gallery/Carousel Section -->
        <section id="gallery" class="gallery py-16 md:py-24" style="background-color: #94a3d6;">
            <div class="container mx-auto px-6 max-w-7xl">
                <div class="section-header text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-bold mb-4" style="color: #556e78;">Championship Gallery</h2>
                    <p class="text-xl max-w-3xl mx-auto leading-relaxed" style="color: #556e78;">Immerse yourself in the breathtaking world of competitive skydiving and the stunning Slovak landscape</p>
                </div>
                <div class="carousel grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div class="carousel-item main-item rounded-3xl shadow-2xl overflow-hidden hover:shadow-3xl transform hover:-translate-y-2 transition-all duration-300 border border-gray-100 relative">
                        <div class="relative overflow-hidden h-full">
                            <img src="images/championship-gallery-1.jpg" alt="Championship action shots" class="carousel-img object-cover h-full w-full hover:scale-110 transition-transform duration-500">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-black/20"></div>
                            <div class="absolute top-6 left-6 text-white px-4 py-2 rounded-xl font-semibold shadow-lg" style="background-color: #b45c4d;">
                                🏆 Competition
                            </div>
                            <div class="absolute bottom-0 left-0 right-0 p-8 text-white">
                                <h3 class="text-2xl font-bold mb-3 text-white">Championship Highlights</h3>
                                <p class="text-lg leading-relaxed mb-6 text-white">Witness the world's best skydivers in action as they compete for precision and style in the ultimate aerial competition.</p>
                                <button class="text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg" style="background-color: #b45c4d;" onmouseover="this.style.backgroundColor='#6acef3'" onmouseout="this.style.backgroundColor='#b45c4d'">
                                    View Photo Gallery
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="carousel-item side-item rounded-3xl shadow-2xl overflow-hidden hover:shadow-3xl transform hover:-translate-y-2 transition-all duration-300 border border-gray-100 relative">
                        <div class="relative overflow-hidden h-full">
                            <img src="images/spiska-nova-ves-landscape.jpg" alt="Spišská Nová Ves landscape" class="carousel-img object-cover h-full w-full hover:scale-110 transition-transform duration-500">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-black/20"></div>
                            <div class="absolute top-6 left-6 text-white px-4 py-2 rounded-xl font-semibold shadow-lg" style="background-color: #b6d88d;">
                                🏔️ Location
                            </div>
                            <div class="absolute bottom-0 left-0 right-0 p-8 text-white">
                                <h3 class="text-2xl font-bold mb-3 text-white">Stunning Location</h3>
                                <p class="text-lg leading-relaxed mb-6 text-white">Experience the natural beauty of Spišská Nová Ves, where pristine landscapes meet world-class sporting excellence.</p>
                                <button class="text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg" style="background-color: #b6d88d;" onmouseover="this.style.backgroundColor='#ffc828'" onmouseout="this.style.backgroundColor='#b6d88d'">
                                    Explore Venue
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer py-16 text-white relative overflow-hidden" style="background-color: #556e78;">
        <div class="container mx-auto px-6 max-w-7xl relative z-10">
            <div class="footer-content text-center space-y-8">
                <div class="footer-header mb-12">
                    <h3 class="text-3xl font-bold text-white mb-4">World Skydiving Championship 2026</h3>
                    <p class="text-xl max-w-2xl mx-auto leading-relaxed" style="color: #b2e0e0;">Join us in Spišská Nová Ves for the ultimate celebration of skydiving excellence</p>
                </div>
                
                <div class="footer-info grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                    <div class="info-block">
                        <div class="icon w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-xl" style="background-color: #6acef3;">
                            <span class="text-2xl">📅</span>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">Competition Dates</h4>
                        <p style="color: #b2e0e0;">June 15-21, 2026</p>
                    </div>
                    <div class="info-block">
                        <div class="icon w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-xl" style="background-color: #b6d88d;">
                            <span class="text-2xl">📍</span>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">Location</h4>
                        <p style="color: #b2e0e0;">Spišská Nová Ves, Slovakia</p>
                    </div>
                    <div class="info-block">
                        <div class="icon w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-xl" style="background-color: #ffc828;">
                            <span class="text-2xl">🏆</span>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">Disciplines</h4>
                        <p style="color: #b2e0e0;">Accuracy Landing & Individual Style</p>
                    </div>
                </div>

                <div class="footer-logos flex justify-center gap-8 mb-8">
                    <div class="logo-item w-16 h-16 rounded-2xl shadow-xl hover:shadow-2xl hover:scale-110 transition-all duration-300 flex items-center justify-center" style="background-color: #94a3d6;">
                        <span class="text-2xl">🪂</span>
                    </div>
                    <div class="logo-item w-16 h-16 rounded-2xl shadow-xl hover:shadow-2xl hover:scale-110 transition-all duration-300 flex items-center justify-center" style="background-color: #b45c4d;">
                        <span class="text-2xl">🌍</span>
                    </div>
                    <div class="logo-item w-16 h-16 rounded-2xl shadow-xl hover:shadow-2xl hover:scale-110 transition-all duration-300 flex items-center justify-center" style="background-color: #6acef3;">
                        <span class="text-2xl">🇸🇰</span>
                    </div>
                </div>
                
                <div class="footer-bottom pt-8" style="border-top: 1px solid #b2e0e0;">
                    <p class="footer-text text-lg" style="color: #b2e0e0;">
                        © 2026 World Skydiving Championship | Organized with passion for aerial excellence
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
