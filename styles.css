/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Modern CSS Custom Properties with Dark Mode Support */
:root {
    /* Primary Brand Colors */
    --sky-calm: #b2e0e0;          /* Sky Calm - C35 M0 Y15 K0 */
    --grass-green: #b6d88d;       /* Grass Green - C40 M0 Y60 K0 */
    --landing-clay: #b45c4d;      /* Landing Clay - C0 M65 Y55 K25 */
    --freefall-blue: #6acef3;     /* Freefall Blue - C55 M0 Y0 K0 */
    --parachute-mist: #94a3d6;    /* Parachute Mist - C45 M30 Y0 K0 */
    --target-yellow: #ffc828;     /* Target Yellow - C0 M25 Y100 K0 */
    --steel-flight: #556e78;      /* Steel Flight - C30 M0 Y0 K60 */

    /* Light Mode Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --border-color: rgba(148, 163, 214, 0.2);
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-medium: rgba(0, 0, 0, 0.15);
    --shadow-heavy: rgba(0, 0, 0, 0.25);

    /* Typography */
    --primary-font: 'Clone Rounded Latin', 'Poppins', sans-serif;
    --secondary-font: 'Poppins', sans-serif;

    /* Modern Spacing Scale */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;

    /* Border Radius Scale */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Animation Durations */
    --duration-fast: 150ms;
    --duration-normal: 300ms;
    --duration-slow: 500ms;

    /* Easing Functions */
    --ease-out: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark Mode Colors */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --border-color: rgba(148, 163, 214, 0.3);
    --shadow-light: rgba(0, 0, 0, 0.3);
    --shadow-medium: rgba(0, 0, 0, 0.4);
    --shadow-heavy: rgba(0, 0, 0, 0.6);
}

/* Automatic Dark Mode Detection */
@media (prefers-color-scheme: dark) {
    :root:not([data-theme="light"]) {
        --bg-primary: #0f172a;
        --bg-secondary: #1e293b;
        --bg-tertiary: #334155;
        --text-primary: #f8fafc;
        --text-secondary: #cbd5e1;
        --text-muted: #94a3b8;
        --border-color: rgba(148, 163, 214, 0.3);
        --shadow-light: rgba(0, 0, 0, 0.3);
        --shadow-medium: rgba(0, 0, 0, 0.4);
        --shadow-heavy: rgba(0, 0, 0, 0.6);
    }
}

body {
    font-family: var(--secondary-font);
    line-height: 1.6;
    color: var(--steel-flight);
    overflow-x: hidden;
    position: relative;
}

/* Geometric Shapes from Logo */
.geometric-shapes {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
    overflow: hidden;
}

.shape {
    position: absolute;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.shape.rectangle {
    width: 40px;
    height: 80px;
    border-radius: 8px;
}

.shape.triangle {
    width: 0;
    height: 0;
    border-style: solid;
}

.shape.triangle-up {
    border-left: 25px solid transparent;
    border-right: 25px solid transparent;
    border-bottom: 50px solid;
}

.shape.triangle-down {
    border-left: 25px solid transparent;
    border-right: 25px solid transparent;
    border-top: 50px solid;
}

/* Logo-inspired colors */
.shape.blue { background-color: var(--freefall-blue); }
.shape.green { background-color: var(--grass-green); }
.shape.yellow { background-color: var(--target-yellow); }
.shape.clay { background-color: var(--landing-clay); }
.shape.mist { background-color: var(--parachute-mist); }
.shape.calm { background-color: var(--sky-calm); }
.shape.steel { background-color: var(--steel-flight); }

.shape.triangle-up.blue { border-bottom-color: var(--freefall-blue); }
.shape.triangle-up.green { border-bottom-color: var(--grass-green); }
.shape.triangle-up.yellow { border-bottom-color: var(--target-yellow); }
.shape.triangle-down.clay { border-top-color: var(--landing-clay); }
.shape.triangle-down.mist { border-top-color: var(--parachute-mist); }
.shape.triangle-down.calm { border-top-color: var(--sky-calm); }

/* Shape positions */
.shape:nth-child(1) { top: 15%; left: 8%; animation-delay: 0s; }
.shape:nth-child(2) { top: 25%; right: 12%; animation-delay: 1s; }
.shape:nth-child(3) { top: 45%; left: 5%; animation-delay: 2s; }
.shape:nth-child(4) { top: 65%; right: 8%; animation-delay: 3s; }
.shape:nth-child(5) { top: 80%; left: 15%; animation-delay: 4s; }
.shape:nth-child(6) { top: 35%; right: 20%; animation-delay: 5s; }
.shape:nth-child(7) { top: 55%; left: 12%; animation-delay: 2.5s; }
.shape:nth-child(8) { top: 75%; right: 25%; animation-delay: 1.5s; }

@keyframes float {
    0%, 100% { 
        transform: translateY(0px) rotate(0deg); 
        opacity: 0.1; 
    }
    50% { 
        transform: translateY(-20px) rotate(5deg); 
        opacity: 0.15; 
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { 
        transform: translateY(0); 
    }
    40% { 
        transform: translateY(-10px); 
    }
    60% { 
        transform: translateY(-5px); 
    }
}

/* Hide shapes on mobile to avoid clutter */
@media (max-width: 768px) {
    .geometric-shapes {
        display: none;
    }
}

/* Image optimization */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

.hero-bg-img,
.section-img,
.discipline-bg,
.hotel-img,
.carousel-img {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.hero-bg-img[src=""],
.section-img[src=""],
.discipline-bg[src=""],
.hotel-img[src=""],
.carousel-img[src=""] {
    opacity: 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Modern Navigation */
.modern-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-nav.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 4px 32px rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
    z-index: 1001;
}

.brand-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--freefall-blue), var(--parachute-mist));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 16px rgba(106, 206, 243, 0.3);
    transition: transform 0.3s ease;
}

.brand-icon:hover {
    transform: scale(1.05) rotate(5deg);
}

.parachute-icon {
    font-size: 1.5rem;
}

.brand-text {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.brand-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--steel-flight);
    margin: 0;
}

.brand-separator {
    color: var(--sky-calm);
    font-weight: 300;
}

.brand-subtitle {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--parachute-mist);
    margin: 0;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    text-decoration: none;
    color: var(--steel-flight);
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(106, 206, 243, 0.1), transparent);
    transition: left 0.5s ease;
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:hover,
.nav-link.active {
    background: rgba(106, 206, 243, 0.1);
    color: var(--freefall-blue);
    transform: translateY(-2px);
}

.nav-link.active {
    background: linear-gradient(135deg, rgba(106, 206, 243, 0.15), rgba(148, 163, 214, 0.1));
    box-shadow: 0 2px 8px rgba(106, 206, 243, 0.2);
}

.nav-icon {
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.nav-link:hover .nav-icon {
    transform: scale(1.2);
}

.nav-text {
    font-weight: 500;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.theme-toggle {
    width: 44px;
    height: 44px;
    border: none;
    border-radius: 12px;
    background: rgba(85, 110, 120, 0.1);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.theme-toggle:hover {
    background: rgba(85, 110, 120, 0.2);
    transform: scale(1.05);
}

.theme-icon {
    font-size: 1.25rem;
    transition: all 0.3s ease;
    position: absolute;
}

.dark-icon {
    opacity: 0;
    transform: rotate(180deg);
}

.theme-toggle.dark .light-icon {
    opacity: 0;
    transform: rotate(180deg);
}

.theme-toggle.dark .dark-icon {
    opacity: 1;
    transform: rotate(0deg);
}

.info-btn,
.register-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
}

.info-btn {
    background: rgba(85, 110, 120, 0.1);
    color: var(--steel-flight);
}

.info-btn:hover {
    background: rgba(85, 110, 120, 0.2);
    transform: translateY(-2px);
}

.register-btn {
    background: linear-gradient(135deg, var(--freefall-blue), var(--parachute-mist));
    color: white;
    box-shadow: 0 4px 16px rgba(106, 206, 243, 0.3);
}

.register-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(106, 206, 243, 0.4);
    background: linear-gradient(135deg, var(--parachute-mist), var(--freefall-blue));
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 44px;
    height: 44px;
    border: none;
    background: none;
    cursor: pointer;
    padding: 0;
    gap: 4px;
}

.hamburger-line {
    width: 24px;
    height: 2px;
    background: var(--steel-flight);
    border-radius: 2px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
}

.mobile-menu-toggle[aria-expanded="true"] .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle[aria-expanded="true"] .hamburger-line:nth-child(2) {
    opacity: 0;
    transform: scaleX(0);
}

.mobile-menu-toggle[aria-expanded="true"] .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

.scroll-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--freefall-blue), var(--parachute-mist), var(--target-yellow));
    transform-origin: left;
    transform: scaleX(0);
    transition: transform 0.1s ease;
}

/* Modern Hero Section */
.modern-hero {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: linear-gradient(135deg, var(--steel-flight) 0%, var(--freefall-blue) 100%);
}


.hero-background {
    position: absolute;
    inset: 0;
    z-index: 1;
    overflow: hidden;
}

.hero-bg-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.8;
    filter: contrast(1.1) brightness(0.85);
    transition: transform var(--duration-slow) var(--ease-out);
}

.hero-bg-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(
        135deg,
        rgba(15, 23, 42, 0.4) 0%,
        rgba(85, 110, 120, 0.3) 30%,
        rgba(106, 206, 243, 0.2) 70%,
        rgba(148, 163, 214, 0.3) 100%
    );
    z-index: 2;
}

.hero-decorations {
    position: absolute;
    inset: 0;
    z-index: 2;
    pointer-events: none;
}

.floating-element {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--freefall-blue), var(--parachute-mist));
    opacity: 0.1;
    animation: float-gentle 8s ease-in-out infinite;
}

.element-1 {
    width: 120px;
    height: 120px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.element-2 {
    width: 80px;
    height: 80px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.element-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float-gentle {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.1;
    }
    50% {
        transform: translateY(-30px) rotate(180deg);
        opacity: 0.2;
    }
}



.hero-content {
    position: relative;
    z-index: 10;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-xl);
}

.hero-container {
    max-width: 1400px;
    width: 100%;
    text-align: center;
    animation: hero-fade-in 1.2s var(--ease-out) forwards;
    opacity: 0;
}

@keyframes hero-fade-in {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-xl);
    margin-bottom: var(--space-2xl);
}

.hero-logo-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-glow {
    position: absolute;
    inset: -20px;
    background: radial-gradient(
        circle,
        rgba(106, 206, 243, 0.3) 0%,
        rgba(148, 163, 214, 0.2) 50%,
        transparent 70%
    );
    border-radius: 50%;
    animation: logo-pulse 3s ease-in-out infinite;
}

@keyframes logo-pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.6;
    }
}

.logo-container {
    position: relative;
    width: 120px;
    height: 120px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-2xl);
    padding: var(--space-md);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: transform var(--duration-normal) var(--ease-out);
}

.logo-container:hover {
    transform: scale(1.05) rotate(5deg);
}

.championship-logo {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: var(--radius-lg);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.hero-title-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-md);
}

.hero-main-title {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-xs);
    margin: 0;
}

.title-line {
    font-family: var(--primary-font);
    font-weight: 900;
    font-size: clamp(2rem, 8vw, 4rem);
    line-height: 0.9;
    letter-spacing: -0.02em;
    position: relative;
    display: inline-block;
    animation: title-reveal 0.8s var(--ease-out) forwards;
    opacity: 0;
    transform: translateY(40px);
}

.title-world {
    color: var(--sky-calm);
    animation-delay: 0.2s;
}

.title-skydiving {
    color: white;
    animation-delay: 0.4s;
}

.title-championship {
    color: var(--target-yellow);
    animation-delay: 0.6s;
}

@keyframes title-reveal {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-year {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-sm);
}

.year-number {
    font-family: var(--primary-font);
    font-weight: 700;
    font-size: clamp(1.25rem, 4vw, 2rem);
    color: var(--parachute-mist);
    animation: year-glow 2s ease-in-out infinite alternate;
}

@keyframes year-glow {
    from {
        text-shadow: 0 0 10px rgba(148, 163, 214, 0.5);
    }
    to {
        text-shadow: 0 0 20px rgba(148, 163, 214, 0.8);
    }
}

.year-underline {
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--freefall-blue), var(--parachute-mist));
    border-radius: 2px;
    animation: underline-expand 1s var(--ease-out) 0.8s forwards;
    transform: scaleX(0);
}

@keyframes underline-expand {
    to {
        transform: scaleX(1);
    }
}


.hero-description {
    font-family: var(--secondary-font);
    font-weight: 400;
    font-size: clamp(1rem, 2.5vw, 1.25rem);
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto var(--space-2xl);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    animation: description-fade-in 1s var(--ease-out) 1s forwards;
    opacity: 0;
}

@keyframes description-fade-in {
    to {
        opacity: 1;
    }
}

.hero-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-lg);
    max-width: 800px;
    margin: 0 auto var(--space-2xl);
}

.info-card {
    position: relative;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--space-lg);
    display: flex;
    align-items: center;
    gap: var(--space-md);
    transition: all var(--duration-normal) var(--ease-out);
    animation: card-slide-up 0.8s var(--ease-out) forwards;
    opacity: 0;
    transform: translateY(40px);
    overflow: hidden;
}

.dates-card {
    animation-delay: 1.2s;
}

.location-card {
    animation-delay: 1.4s;
}

.disciplines-card {
    animation-delay: 1.6s;
}

@keyframes card-slide-up {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.info-card:hover {
    transform: translateY(-8px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3);
}

.card-icon {
    font-size: 2rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.card-content {
    flex: 1;
    text-align: left;
}

.card-title {
    font-family: var(--secondary-font);
    font-weight: 600;
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0 0 var(--space-xs);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.card-value {
    font-family: var(--primary-font);
    font-weight: 700;
    font-size: 1rem;
    margin: 0;
    color: white;
}

.dates-card .card-value {
    color: var(--sky-calm);
}

.location-card .card-value {
    color: var(--grass-green);
}

.disciplines-card .card-value {
    color: var(--target-yellow);
}

.card-glow {
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    opacity: 0;
    transition: opacity var(--duration-normal) var(--ease-out);
}

.info-card:hover .card-glow {
    opacity: 1;
}

.hero-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: var(--space-lg);
    margin-bottom: var(--space-3xl);
}

.hero-btn {
    position: relative;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-lg) var(--space-2xl);
    border: none;
    border-radius: var(--radius-xl);
    font-family: var(--primary-font);
    font-weight: 700;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
    overflow: hidden;
    animation: button-fade-in 0.8s var(--ease-out) forwards;
    opacity: 0;
    transform: translateY(20px);
}

.primary-btn {
    background: linear-gradient(135deg, var(--freefall-blue), var(--parachute-mist));
    color: white;
    box-shadow: 0 8px 24px rgba(106, 206, 243, 0.4);
    animation-delay: 1.8s;
}

.secondary-btn {
    background: rgba(180, 92, 77, 0.2);
    color: white;
    border: 2px solid var(--landing-clay);
    animation-delay: 2s;
}

@keyframes button-fade-in {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-btn:hover {
    transform: translateY(-4px) scale(1.05);
}

.primary-btn:hover {
    box-shadow: 0 12px 32px rgba(106, 206, 243, 0.6);
    background: linear-gradient(135deg, var(--parachute-mist), var(--freefall-blue));
}

.secondary-btn:hover {
    background: rgba(180, 92, 77, 0.3);
    border-color: var(--target-yellow);
}

.btn-icon {
    font-size: 1.25rem;
    transition: transform var(--duration-normal) var(--ease-out);
}

.hero-btn:hover .btn-icon {
    transform: scale(1.2) rotate(10deg);
}

.btn-ripple {
    position: absolute;
    inset: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    opacity: 0;
    transform: scale(0);
    transition: all var(--duration-fast) var(--ease-out);
}

.hero-btn:active .btn-ripple {
    opacity: 1;
    transform: scale(1);
}


.scroll-indicator {
    position: absolute;
    bottom: var(--space-2xl);
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-sm);
    color: rgba(255, 255, 255, 0.7);
    animation: scroll-fade-in 1s var(--ease-out) 2.5s forwards;
    opacity: 0;
}

@keyframes scroll-fade-in {
    to {
        opacity: 1;
    }
}

.scroll-text {
    font-family: var(--secondary-font);
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: var(--space-sm);
}

.scroll-mouse {
    width: 24px;
    height: 40px;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 12px;
    position: relative;
    animation: mouse-bounce 2s ease-in-out infinite;
}

.scroll-wheel {
    width: 4px;
    height: 8px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 2px;
    position: absolute;
    top: 6px;
    left: 50%;
    transform: translateX(-50%);
    animation: wheel-scroll 2s ease-in-out infinite;
}

@keyframes mouse-bounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes wheel-scroll {
    0% {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
    50% {
        opacity: 0.5;
        transform: translateX(-50%) translateY(8px);
    }
    100% {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

.scroll-arrow {
    width: 20px;
    height: 20px;
    color: rgba(255, 255, 255, 0.6);
    animation: arrow-bounce 1.5s ease-in-out infinite;
}

@keyframes arrow-bounce {
    0%, 100% {
        transform: translateY(0);
        opacity: 0.6;
    }
    50% {
        transform: translateY(5px);
        opacity: 1;
    }
}



.hero-logo-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 420px;
    min-height: 320px;
    z-index: 4;
}


.logo-motif-bg {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 320px;
    height: 320px;
    border-radius: 50%;
    background: radial-gradient(circle at 60% 40%, var(--sky-calm) 0%, var(--parachute-mist) 40%, var(--freefall-blue) 80%, transparent 100%);
    opacity: 0.18;
    z-index: 2;
    pointer-events: none;
    box-shadow: 0 0 0 12px rgba(255,255,255,0.07);
}


.logo-multicolor-glow {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 340px;
    height: 340px;
    border-radius: 50%;
    z-index: 3;
    pointer-events: none;
    background: conic-gradient(
        var(--target-yellow) 0deg 45deg,
        var(--grass-green) 45deg 90deg,
        var(--parachute-mist) 90deg 135deg,
        var(--landing-clay) 135deg 180deg,
        var(--freefall-blue) 180deg 225deg,
        var(--sky-calm) 225deg 270deg,
        var(--steel-flight) 270deg 315deg,
        var(--target-yellow) 315deg 360deg
    );
    filter: blur(32px) brightness(1.2);
    opacity: 0.18;
    animation: glow-rotate 12s linear infinite;
}

@keyframes glow-rotate {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.hero-logo {
    position: relative;
    z-index: 3;
    max-width: 340px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-img {
    width: 100%;
    height: auto;
    max-width: 340px;
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.18));
    border-radius: 50%;
    background: white;
    box-shadow: 0 0 0 8px rgba(255,255,255,0.12);
    opacity: 0;
    animation: fadeIn 1.2s 0.3s ease-out forwards;
}

.fade-in-logo {
    opacity: 0;
    animation: fadeIn 1.2s 0.3s ease-out forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: scale(0.96); }
    to { opacity: 1; transform: scale(1); }
}


.hero-title {
    font-family: var(--primary-font);
    font-weight: 600;
    font-size: clamp(28px, 5vw, 48px);
    background: linear-gradient(135deg, var(--target-yellow) 0%, var(--freefall-blue) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 30px;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    color: #fff;
    opacity: 0;
    animation: fadeIn 1.1s 0.1s ease-out forwards;
}

/* Support for browsers without background-clip */
@supports not (-webkit-background-clip: text) {
    .hero-title {
        color: var(--target-yellow);
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }
}


.hero-description {
    font-family: var(--secondary-font);
    font-weight: 300;
    font-size: clamp(14px, 2vw, 22px);
    color: white;
    margin-bottom: 40px;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.4;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
    opacity: 0;
    animation: fadeIn 1.1s 0.2s ease-out forwards;
}

/* Hero Buttons */
.hero-buttons {
    display: flex;
    justify-content: flex-start;
    gap: 20px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.hero-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 18px 35px;
    border: none;
    border-radius: 50px;
    font-family: var(--secondary-font);
    font-weight: 600;
    font-size: 16px;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.hero-btn.primary {
    background: linear-gradient(135deg, var(--landing-clay), var(--target-yellow));
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.hero-btn.primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(180, 92, 77, 0.4);
    background: linear-gradient(135deg, #c54b3a, var(--target-yellow));
}

.hero-btn.secondary {
    background: linear-gradient(135deg, var(--parachute-mist), var(--freefall-blue));
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.hero-btn.secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(148, 163, 214, 0.4);
    background: linear-gradient(135deg, #8493d0, #5ac8ed);
}

.btn-icon {
    font-size: 18px;
}

.hero-info {
    display: flex;
    justify-content: flex-start;
    margin-top: 20px;
}

.date-location {
    display: flex;
    gap: 30px;
    align-items: center;
    flex-wrap: wrap;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-icon {
    font-size: 16px;
}

.info-text {
    font-family: var(--secondary-font);
    font-weight: 500;
    font-size: 16px;
    color: white;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
}

/* Main Content */
.main-content {
    padding-top: 100px;
}

.section {
    padding: 80px 0;
    background: white;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.text-column {
    padding-right: 40px;
}

.section-title {
    font-family: var(--primary-font);
    font-weight: 600;
    font-size: clamp(32px, 4vw, 40px);
    color: var(--steel-flight);
    margin-bottom: 20px;
    line-height: 1.3;
    position: relative;
    padding-left: 50px;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 6px;
    background: linear-gradient(45deg, var(--target-yellow), var(--freefall-blue));
    border-radius: 3px;
}

.section-title::after {
    content: '';
    position: absolute;
    left: 8px;
    top: 20%;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 14px solid var(--grass-green);
    opacity: 0.8;
}

.section-date {
    font-family: var(--secondary-font);
    font-weight: 500;
    font-size: 18px;
    color: var(--landing-clay);
    margin-bottom: 20px;
    letter-spacing: 0.8px;
}

.section-text {
    font-family: var(--secondary-font);
    font-weight: 400;
    font-size: 20px;
    color: var(--steel-flight);
    line-height: 1.6;
    margin-bottom: 30px;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, var(--grass-green), var(--target-yellow));
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-family: var(--secondary-font);
    font-weight: 500;
    font-size: 14px;
    letter-spacing: 0.1px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(182, 216, 141, 0.3);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(182, 216, 141, 0.4);
    background: linear-gradient(135deg, #a6cf77, #ffdb38);
}

.button-icon {
    font-size: 16px;
}

.image-column {
    position: relative;
}

.section-image {
    width: 100%;
    height: 400px;
    background: linear-gradient(135deg, #ece6f0 0%, #d18232 100%);
    border-radius: 28px;
    position: relative;
    overflow: hidden;
}

.section-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 28px;
    transition: transform 0.3s ease;
}

.section-image:hover .section-img {
    transform: scale(1.05);
}

.section-image::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(236, 230, 240, 0.3) 0%, rgba(209, 130, 50, 0.3) 100%);
    z-index: 1;
    pointer-events: none;
}

/* Modern Disciplines Section */
.modern-disciplines-section {
    padding: var(--space-3xl) 0;
    background: var(--grass-green);
    position: relative;
    overflow: hidden;
}

.modern-disciplines-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(182, 216, 141, 0.9) 0%,
        rgba(148, 163, 214, 0.1) 50%,
        rgba(106, 206, 243, 0.1) 100%
    );
    z-index: 1;
}

.disciplines-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-xl);
    position: relative;
    z-index: 2;
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-3xl);
}

.section-title {
    font-family: var(--primary-font);
    font-weight: 800;
    font-size: clamp(2.5rem, 6vw, 4rem);
    color: var(--steel-flight);
    margin-bottom: var(--space-lg);
    position: relative;
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--freefall-blue), var(--parachute-mist));
    border-radius: 2px;
}

.section-description {
    font-family: var(--secondary-font);
    font-size: clamp(1rem, 2vw, 1.25rem);
    color: var(--steel-flight);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
    opacity: 0.9;
}

.disciplines-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--space-2xl);
    max-width: 1000px;
    margin: 0 auto;
}

.modern-discipline-card {
    position: relative;
    background: var(--bg-primary);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: 0 8px 32px var(--shadow-medium);
    transition: all var(--duration-normal) var(--ease-out);
    cursor: pointer;
    height: 500px;
}

.modern-discipline-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 20px 60px var(--shadow-heavy);
}

.card-background {
    position: absolute;
    inset: 0;
    z-index: 1;
}

.card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--duration-slow) var(--ease-out);
}

.modern-discipline-card:hover .card-image {
    transform: scale(1.1);
}

.card-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.3) 0%,
        rgba(0, 0, 0, 0.6) 70%,
        rgba(0, 0, 0, 0.8) 100%
    );
    z-index: 2;
}

.card-content {
    position: absolute;
    inset: 0;
    z-index: 3;
    padding: var(--space-2xl);
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    color: white;
}

.card-header {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-lg);
}

.discipline-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    transition: transform var(--duration-normal) var(--ease-out);
}

.accuracy-icon {
    background: linear-gradient(135deg, var(--landing-clay), var(--target-yellow));
}

.style-icon {
    background: linear-gradient(135deg, var(--parachute-mist), var(--freefall-blue));
}

.modern-discipline-card:hover .discipline-icon {
    transform: scale(1.1) rotate(10deg);
}

.discipline-title {
    font-family: var(--primary-font);
    font-weight: 700;
    font-size: 1.75rem;
    margin: 0;
    color: white;
}

.discipline-description {
    font-family: var(--secondary-font);
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: var(--space-lg);
    opacity: 0.9;
}

.card-stats {
    display: flex;
    gap: var(--space-lg);
    margin-bottom: var(--space-lg);
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--space-sm) var(--space-md);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-value {
    font-family: var(--primary-font);
    font-weight: 700;
    font-size: 1.25rem;
    color: white;
}

.stat-label {
    font-family: var(--secondary-font);
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.discipline-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-md) var(--space-lg);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    color: white;
    font-family: var(--secondary-font);
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
}

.discipline-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.btn-arrow {
    font-size: 1.25rem;
    transition: transform var(--duration-normal) var(--ease-out);
}

.discipline-btn:hover .btn-arrow {
    transform: translateX(4px);
}

.card-glow-effect {
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg, var(--freefall-blue), var(--parachute-mist), var(--target-yellow));
    border-radius: var(--radius-2xl);
    opacity: 0;
    z-index: 0;
    transition: opacity var(--duration-normal) var(--ease-out);
}

.modern-discipline-card:hover .card-glow-effect {
    opacity: 0.3;
}

/* Accommodation Section */
.accommodation {
    background: rgba(178, 224, 224, 0.1);
}

.hotels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.hotel-card {
    background: white;
    border-radius: 28px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(85, 110, 120, 0.1);
    transition: transform 0.3s ease;
    border: 1px solid rgba(178, 224, 224, 0.3);
}

.hotel-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(85, 110, 120, 0.15);
}

.hotel-image {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, var(--sky-calm) 0%, var(--grass-green) 30%, var(--freefall-blue) 100%);
    position: relative;
    overflow: hidden;
}

.hotel-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.hotel-card:hover .hotel-img {
    transform: scale(1.1);
}

.hotel-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(178, 224, 224, 0.2) 0%, rgba(182, 216, 141, 0.3) 30%, rgba(106, 206, 243, 0.2) 100%);
    z-index: 1;
    pointer-events: none;
}

.hotel-info {
    padding: 20px;
}

.hotel-name {
    font-family: var(--primary-font);
    font-weight: 600;
    font-size: 20px;
    color: var(--steel-flight);
    margin-bottom: 10px;
}

.hotel-distance {
    font-family: var(--secondary-font);
    font-weight: 500;
    font-size: 16px;
    color: var(--landing-clay);
}

/* Gallery Section */
.gallery {
    padding: 100px 0;
    background: var(--steel-flight);
}

.carousel {
    display: grid;
    grid-template-columns: 3fr 1fr;
    gap: 40px;
    height: 600px;
}

.carousel-item {
    border-radius: 90px;
    overflow: hidden;
    position: relative;
    background: linear-gradient(135deg, var(--sky-calm) 0%, var(--parachute-mist) 50%, var(--freefall-blue) 100%);
}

.carousel-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
}

.carousel-content {
    position: absolute;
    inset: 0;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2;
}

.carousel-content h3 {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: 36px;
    color: white;
    margin-bottom: 20px;
}

.carousel-content p {
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    font-size: 18px;
    color: white;
    line-height: 1.6;
}

/* Footer */
.footer {
    background: var(--steel-flight);
    padding: 60px 0;
    color: white;
}

.footer-content {
    text-align: center;
}

.footer-logos {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 30px;
}

.logo-item {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--target-yellow), var(--freefall-blue));
    border-radius: 50%;
    position: relative;
}

.logo-item::before {
    content: '🪂';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 30px;
}

.footer-text {
    font-family: var(--secondary-font);
    font-weight: 400;
    font-size: 16px;
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 900px) {
    .hero-content {
        padding: 32px 8px 32px 8px;
    }
    .hero-layout {
        gap: 32px;
    }
    .hero-left, .hero-right {
        padding: 0;
    }
}

/* Mobile Navigation Styles */
@media (max-width: 1024px) {
    .nav-container {
        padding: 0 1.5rem;
    }

    .nav-links {
        gap: 0.25rem;
    }

    .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }

    .nav-text {
        display: none;
    }

    .nav-icon {
        font-size: 1.1rem;
    }
}

@media (max-width: 768px) {
    .nav-container {
        height: 70px;
        padding: 0 1rem;
    }

    .brand-title {
        font-size: 1.1rem;
    }

    .brand-subtitle {
        font-size: 0.8rem;
    }

    .nav-links {
        position: fixed;
        top: 70px;
        left: 0;
        right: 0;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        flex-direction: column;
        padding: 2rem 1rem;
        gap: 1rem;
        transform: translateY(-100vh);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .nav-links.mobile-open {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-link {
        width: 100%;
        padding: 1rem;
        justify-content: flex-start;
        border-radius: 16px;
        font-size: 1rem;
    }

    .nav-text {
        display: block;
    }

    .nav-icon {
        font-size: 1.25rem;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .info-btn {
        display: none;
    }

    .register-btn {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    .btn-text {
        display: none;
    }

    .modern-hero {
        min-height: 100vh;
        padding-top: 70px;
    }

    .hero-content {
        padding: var(--space-lg);
    }

    .hero-header {
        flex-direction: column;
        gap: var(--space-lg);
        margin-bottom: var(--space-xl);
    }

    .logo-container {
        width: 80px;
        height: 80px;
    }

    .hero-main-title .title-line {
        font-size: clamp(1.5rem, 6vw, 2.5rem);
    }

    .hero-info-grid {
        grid-template-columns: 1fr;
        gap: var(--space-md);
        max-width: 400px;
    }

    .info-card {
        padding: var(--space-md);
        flex-direction: column;
        text-align: center;
        gap: var(--space-sm);
    }

    .card-content {
        text-align: center;
    }

    .hero-actions {
        flex-direction: column;
        gap: var(--space-md);
    }

    .hero-btn {
        width: 100%;
        max-width: 280px;
        padding: var(--space-md) var(--space-lg);
        font-size: 0.875rem;
    }

    .scroll-indicator {
        bottom: var(--space-xl);
    }
    .hero-content {
        padding: 16px 4px 16px 4px;
    }
    .hero-layout {
        grid-template-columns: 1fr;
        gap: 32px;
        text-align: center;
        min-height: unset;
    }
    .hero-left {
        text-align: center;
        order: 2;
        padding: 0;
    }
    .hero-right {
        order: 1;
        padding: 0;
    }
    .hero-logo {
        max-width: 220px;
    }
    .hero-buttons {
        justify-content: center;
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }
    .date-location {
        justify-content: center;
    }

    .hero-btn {
        width: 100%;
        max-width: 280px;
        padding: 16px 30px;
        font-size: 14px;
    }

    .date-location {
        flex-direction: column;
        gap: 20px;
    }

    .info-item {
        width: 100%;
    }

    .date, .location {
        width: 100%;
        max-width: 250px;
        text-align: center;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .text-column {
        padding-right: 0;
    }

    .disciplines-grid {
        grid-template-columns: 1fr;
        gap: var(--space-xl);
    }

    .modern-discipline-card {
        height: 400px;
    }

    .card-content {
        padding: var(--space-lg);
    }

    .card-header {
        margin-bottom: var(--space-md);
    }

    .discipline-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .discipline-title {
        font-size: 1.5rem;
    }

    .card-stats {
        gap: var(--space-md);
        margin-bottom: var(--space-md);
    }

    .stat-item {
        padding: var(--space-xs) var(--space-sm);
    }

    .stat-value {
        font-size: 1rem;
    }

    .carousel {
        grid-template-columns: 1fr;
        height: auto;
    }

    .carousel-item {
        height: 300px;
    }

    .hotels-grid {
        grid-template-columns: 1fr;
    }

    .footer-logos {
        flex-direction: row;
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .hero-content {
        padding: 120px 15px 0;
    }
    
    .section {
        padding: 60px 0;
    }
    
    .disciplines-section {
        padding: 80px 0;
    }
    
    .gallery {
        padding: 80px 0;
    }
}

/* Animation for scroll */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.section {
    animation: fadeInUp 0.6s ease-out;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}
