/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Official Brand Colors from Style Manual */
:root {
    /* Primary Brand Colors */
    --sky-calm: #b2e0e0;          /* Sky Calm - C35 M0 Y15 K0 */
    --grass-green: #b6d88d;       /* Grass Green - C40 M0 Y60 K0 */
    --landing-clay: #b45c4d;      /* Landing Clay - C0 M65 Y55 K25 */
    --freefall-blue: #6acef3;     /* Freefall Blue - C55 M0 Y0 K0 */
    --parachute-mist: #94a3d6;    /* Parachute Mist - C45 M30 Y0 K0 */
    --target-yellow: #ffc828;     /* Target Yellow - C0 M25 Y100 K0 */
    --steel-flight: #556e78;      /* Steel Flight - C30 M0 Y0 K60 */
    
    /* Typography */
    --primary-font: 'Clone Rounded Latin', 'Poppins', sans-serif;
    --secondary-font: 'Poppins', sans-serif;
}

body {
    font-family: var(--secondary-font);
    line-height: 1.6;
    color: var(--steel-flight);
    overflow-x: hidden;
    position: relative;
}

/* Geometric Shapes from Logo */
.geometric-shapes {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
    overflow: hidden;
}

.shape {
    position: absolute;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.shape.rectangle {
    width: 40px;
    height: 80px;
    border-radius: 8px;
}

.shape.triangle {
    width: 0;
    height: 0;
    border-style: solid;
}

.shape.triangle-up {
    border-left: 25px solid transparent;
    border-right: 25px solid transparent;
    border-bottom: 50px solid;
}

.shape.triangle-down {
    border-left: 25px solid transparent;
    border-right: 25px solid transparent;
    border-top: 50px solid;
}

/* Logo-inspired colors */
.shape.blue { background-color: var(--freefall-blue); }
.shape.green { background-color: var(--grass-green); }
.shape.yellow { background-color: var(--target-yellow); }
.shape.clay { background-color: var(--landing-clay); }
.shape.mist { background-color: var(--parachute-mist); }
.shape.calm { background-color: var(--sky-calm); }
.shape.steel { background-color: var(--steel-flight); }

.shape.triangle-up.blue { border-bottom-color: var(--freefall-blue); }
.shape.triangle-up.green { border-bottom-color: var(--grass-green); }
.shape.triangle-up.yellow { border-bottom-color: var(--target-yellow); }
.shape.triangle-down.clay { border-top-color: var(--landing-clay); }
.shape.triangle-down.mist { border-top-color: var(--parachute-mist); }
.shape.triangle-down.calm { border-top-color: var(--sky-calm); }

/* Shape positions */
.shape:nth-child(1) { top: 15%; left: 8%; animation-delay: 0s; }
.shape:nth-child(2) { top: 25%; right: 12%; animation-delay: 1s; }
.shape:nth-child(3) { top: 45%; left: 5%; animation-delay: 2s; }
.shape:nth-child(4) { top: 65%; right: 8%; animation-delay: 3s; }
.shape:nth-child(5) { top: 80%; left: 15%; animation-delay: 4s; }
.shape:nth-child(6) { top: 35%; right: 20%; animation-delay: 5s; }
.shape:nth-child(7) { top: 55%; left: 12%; animation-delay: 2.5s; }
.shape:nth-child(8) { top: 75%; right: 25%; animation-delay: 1.5s; }

@keyframes float {
    0%, 100% { 
        transform: translateY(0px) rotate(0deg); 
        opacity: 0.1; 
    }
    50% { 
        transform: translateY(-20px) rotate(5deg); 
        opacity: 0.15; 
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { 
        transform: translateY(0); 
    }
    40% { 
        transform: translateY(-10px); 
    }
    60% { 
        transform: translateY(-5px); 
    }
}

/* Hide shapes on mobile to avoid clutter */
@media (max-width: 768px) {
    .geometric-shapes {
        display: none;
    }
}

/* Image optimization */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

.hero-bg-img,
.section-img,
.discipline-bg,
.hotel-img,
.carousel-img {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.hero-bg-img[src=""],
.section-img[src=""],
.discipline-bg[src=""],
.hotel-img[src=""],
.carousel-img[src=""] {
    opacity: 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navigation {
    background: linear-gradient(135deg, var(--freefall-blue), var(--sky-calm));
    padding: 20px 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(85, 110, 120, 0.3);
}

.nav-container {
    display: flex;
    justify-content: center;
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-item {
    cursor: pointer;
    transition: transform 0.3s ease;
}

.nav-item:hover {
    transform: translateY(-2px);
}

.nav-icon-container {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(178, 224, 224, 0.9);
    padding: 10px 20px;
    border-radius: 20px;
    transition: all 0.3s ease;
    color: var(--steel-flight);
}

.nav-item.active .nav-icon-container {
    background: var(--target-yellow);
    box-shadow: 0 4px 15px rgba(255, 200, 40, 0.3);
    color: var(--steel-flight);
}

.nav-item:hover .nav-icon-container {
    background: rgba(255, 200, 40, 0.8);
}

.nav-icon {
    font-size: 18px;
}

.nav-label {
    font-family: var(--secondary-font);
    font-weight: 500;
    font-size: 12px;
    color: #4a4459;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

/* Hero Section */

.hero {
    min-height: 90vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--steel-flight) 0%, var(--freefall-blue) 100%);
    overflow: hidden;
    padding-top: 120px;
    padding-bottom: 60px;
    margin-bottom: 40px;
}


.hero-background {
    position: absolute;
    inset: 0;
    overflow: hidden;
    z-index: 1;
}

.hero-bg-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.9;
    filter: contrast(1.1) brightness(0.9);
}


.hero-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(85, 110, 120, 0.3) 0%, rgba(106, 206, 243, 0.4) 100%);
    z-index: 2;
}



.hero-content {
    position: relative;
    z-index: 3;
    max-width: 1200px;
    padding: 40px 32px 40px 32px;
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;
}


.hero-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 64px;
    align-items: center;
    min-height: 420px;
}


.hero-left {
    text-align: left;
    padding-right: 32px;
    padding-left: 8px;
}


.hero-right {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-left: 32px;
    padding-right: 8px;
}



.hero-logo-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 420px;
    min-height: 320px;
    z-index: 4;
}


.logo-motif-bg {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 320px;
    height: 320px;
    border-radius: 50%;
    background: radial-gradient(circle at 60% 40%, var(--sky-calm) 0%, var(--parachute-mist) 40%, var(--freefall-blue) 80%, transparent 100%);
    opacity: 0.18;
    z-index: 2;
    pointer-events: none;
    box-shadow: 0 0 0 12px rgba(255,255,255,0.07);
}


.logo-multicolor-glow {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 340px;
    height: 340px;
    border-radius: 50%;
    z-index: 3;
    pointer-events: none;
    background: conic-gradient(
        var(--target-yellow) 0deg 45deg,
        var(--grass-green) 45deg 90deg,
        var(--parachute-mist) 90deg 135deg,
        var(--landing-clay) 135deg 180deg,
        var(--freefall-blue) 180deg 225deg,
        var(--sky-calm) 225deg 270deg,
        var(--steel-flight) 270deg 315deg,
        var(--target-yellow) 315deg 360deg
    );
    filter: blur(32px) brightness(1.2);
    opacity: 0.18;
    animation: glow-rotate 12s linear infinite;
}

@keyframes glow-rotate {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.hero-logo {
    position: relative;
    z-index: 3;
    max-width: 340px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-img {
    width: 100%;
    height: auto;
    max-width: 340px;
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.18));
    border-radius: 50%;
    background: white;
    box-shadow: 0 0 0 8px rgba(255,255,255,0.12);
    opacity: 0;
    animation: fadeIn 1.2s 0.3s ease-out forwards;
}

.fade-in-logo {
    opacity: 0;
    animation: fadeIn 1.2s 0.3s ease-out forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: scale(0.96); }
    to { opacity: 1; transform: scale(1); }
}


.hero-title {
    font-family: var(--primary-font);
    font-weight: 600;
    font-size: clamp(28px, 5vw, 48px);
    background: linear-gradient(135deg, var(--target-yellow) 0%, var(--freefall-blue) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 30px;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    color: #fff;
    opacity: 0;
    animation: fadeIn 1.1s 0.1s ease-out forwards;
}

/* Support for browsers without background-clip */
@supports not (-webkit-background-clip: text) {
    .hero-title {
        color: var(--target-yellow);
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }
}


.hero-description {
    font-family: var(--secondary-font);
    font-weight: 300;
    font-size: clamp(14px, 2vw, 22px);
    color: white;
    margin-bottom: 40px;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.4;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
    opacity: 0;
    animation: fadeIn 1.1s 0.2s ease-out forwards;
}

/* Hero Buttons */
.hero-buttons {
    display: flex;
    justify-content: flex-start;
    gap: 20px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.hero-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 18px 35px;
    border: none;
    border-radius: 50px;
    font-family: var(--secondary-font);
    font-weight: 600;
    font-size: 16px;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.hero-btn.primary {
    background: linear-gradient(135deg, var(--landing-clay), var(--target-yellow));
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.hero-btn.primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(180, 92, 77, 0.4);
    background: linear-gradient(135deg, #c54b3a, var(--target-yellow));
}

.hero-btn.secondary {
    background: linear-gradient(135deg, var(--parachute-mist), var(--freefall-blue));
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.hero-btn.secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(148, 163, 214, 0.4);
    background: linear-gradient(135deg, #8493d0, #5ac8ed);
}

.btn-icon {
    font-size: 18px;
}

.hero-info {
    display: flex;
    justify-content: flex-start;
    margin-top: 20px;
}

.date-location {
    display: flex;
    gap: 30px;
    align-items: center;
    flex-wrap: wrap;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-icon {
    font-size: 16px;
}

.info-text {
    font-family: var(--secondary-font);
    font-weight: 500;
    font-size: 16px;
    color: white;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
}

/* Main Content */
.main-content {
    padding-top: 100px;
}

.section {
    padding: 80px 0;
    background: white;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.text-column {
    padding-right: 40px;
}

.section-title {
    font-family: var(--primary-font);
    font-weight: 600;
    font-size: clamp(32px, 4vw, 40px);
    color: var(--steel-flight);
    margin-bottom: 20px;
    line-height: 1.3;
    position: relative;
    padding-left: 50px;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 6px;
    background: linear-gradient(45deg, var(--target-yellow), var(--freefall-blue));
    border-radius: 3px;
}

.section-title::after {
    content: '';
    position: absolute;
    left: 8px;
    top: 20%;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 14px solid var(--grass-green);
    opacity: 0.8;
}

.section-date {
    font-family: var(--secondary-font);
    font-weight: 500;
    font-size: 18px;
    color: var(--landing-clay);
    margin-bottom: 20px;
    letter-spacing: 0.8px;
}

.section-text {
    font-family: var(--secondary-font);
    font-weight: 400;
    font-size: 20px;
    color: var(--steel-flight);
    line-height: 1.6;
    margin-bottom: 30px;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, var(--grass-green), var(--target-yellow));
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-family: var(--secondary-font);
    font-weight: 500;
    font-size: 14px;
    letter-spacing: 0.1px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(182, 216, 141, 0.3);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(182, 216, 141, 0.4);
    background: linear-gradient(135deg, #a6cf77, #ffdb38);
}

.button-icon {
    font-size: 16px;
}

.image-column {
    position: relative;
}

.section-image {
    width: 100%;
    height: 400px;
    background: linear-gradient(135deg, #ece6f0 0%, #d18232 100%);
    border-radius: 28px;
    position: relative;
    overflow: hidden;
}

.section-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 28px;
    transition: transform 0.3s ease;
}

.section-image:hover .section-img {
    transform: scale(1.05);
}

.section-image::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(236, 230, 240, 0.3) 0%, rgba(209, 130, 50, 0.3) 100%);
    z-index: 1;
    pointer-events: none;
}

/* Disciplines Section */
.disciplines-section {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--steel-flight) 0%, var(--freefall-blue) 100%);
    position: relative;
    overflow: hidden;
}

.disciplines-section::before {
    content: '';
    position: absolute;
    top: 10%;
    right: 5%;
    width: 60px;
    height: 120px;
    background: var(--target-yellow);
    border-radius: 10px;
    opacity: 0.1;
    transform: rotate(15deg);
}

.disciplines-section::after {
    content: '';
    position: absolute;
    bottom: 15%;
    left: 8%;
    width: 0;
    height: 0;
    border-left: 40px solid transparent;
    border-right: 40px solid transparent;
    border-bottom: 70px solid var(--grass-green);
    opacity: 0.1;
    transform: rotate(-10deg);
}

.disciplines-title {
    font-family: var(--primary-font);
    font-weight: 600;
    font-size: clamp(48px, 8vw, 96px);
    background: linear-gradient(135deg, var(--target-yellow) 0%, var(--freefall-blue) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-align: center;
    margin-bottom: 60px;
}

.disciplines-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    max-width: 1000px;
    margin: 0 auto;
}

.discipline-card {
    height: 400px;
    border-radius: 75px;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.discipline-card:hover {
    transform: scale(1.02);
}

.discipline-bg {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
}

.discipline-card.accuracy {
    background: linear-gradient(135deg, rgba(209, 130, 50, 0.7), rgba(209, 148, 25, 0.8));
    border: 2px solid #d18232;
}

.discipline-card.accuracy .discipline-overlay {
    background: linear-gradient(135deg, rgba(209, 130, 50, 0.8), rgba(209, 148, 25, 0.9));
}

.discipline-card.style {
    background: linear-gradient(135deg, rgba(69, 127, 189, 0.7), rgba(3, 63, 136, 0.8));
    border: 2px solid #033f88;
}

.discipline-card.style .discipline-overlay {
    background: linear-gradient(135deg, rgba(69, 127, 189, 0.8), rgba(3, 63, 136, 0.9));
}

.discipline-overlay {
    position: absolute;
    inset: 0;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    z-index: 2;
}

.discipline-overlay h3 {
    font-family: 'Roboto', sans-serif;
    font-weight: 900;
    font-size: 40px;
    color: #fff;
    margin-bottom: 20px;
    letter-spacing: 2px;
}

.discipline-overlay p {
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    font-size: 18px;
    color: #fff;
    line-height: 1.6;
    max-width: 300px;
}

/* Accommodation Section */
.accommodation {
    background: rgba(178, 224, 224, 0.1);
}

.hotels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.hotel-card {
    background: white;
    border-radius: 28px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(85, 110, 120, 0.1);
    transition: transform 0.3s ease;
    border: 1px solid rgba(178, 224, 224, 0.3);
}

.hotel-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(85, 110, 120, 0.15);
}

.hotel-image {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, var(--sky-calm) 0%, var(--grass-green) 30%, var(--freefall-blue) 100%);
    position: relative;
    overflow: hidden;
}

.hotel-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.hotel-card:hover .hotel-img {
    transform: scale(1.1);
}

.hotel-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(178, 224, 224, 0.2) 0%, rgba(182, 216, 141, 0.3) 30%, rgba(106, 206, 243, 0.2) 100%);
    z-index: 1;
    pointer-events: none;
}

.hotel-info {
    padding: 20px;
}

.hotel-name {
    font-family: var(--primary-font);
    font-weight: 600;
    font-size: 20px;
    color: var(--steel-flight);
    margin-bottom: 10px;
}

.hotel-distance {
    font-family: var(--secondary-font);
    font-weight: 500;
    font-size: 16px;
    color: var(--landing-clay);
}

/* Gallery Section */
.gallery {
    padding: 100px 0;
    background: var(--steel-flight);
}

.carousel {
    display: grid;
    grid-template-columns: 3fr 1fr;
    gap: 40px;
    height: 600px;
}

.carousel-item {
    border-radius: 90px;
    overflow: hidden;
    position: relative;
    background: linear-gradient(135deg, var(--sky-calm) 0%, var(--parachute-mist) 50%, var(--freefall-blue) 100%);
}

.carousel-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
}

.carousel-content {
    position: absolute;
    inset: 0;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2;
}

.carousel-content h3 {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: 36px;
    color: white;
    margin-bottom: 20px;
}

.carousel-content p {
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    font-size: 18px;
    color: white;
    line-height: 1.6;
}

/* Footer */
.footer {
    background: var(--steel-flight);
    padding: 60px 0;
    color: white;
}

.footer-content {
    text-align: center;
}

.footer-logos {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 30px;
}

.logo-item {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--target-yellow), var(--freefall-blue));
    border-radius: 50%;
    position: relative;
}

.logo-item::before {
    content: '🪂';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 30px;
}

.footer-text {
    font-family: var(--secondary-font);
    font-weight: 400;
    font-size: 16px;
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 900px) {
    .hero-content {
        padding: 32px 8px 32px 8px;
    }
    .hero-layout {
        gap: 32px;
    }
    .hero-left, .hero-right {
        padding: 0;
    }
}

@media (max-width: 768px) {
    .hero {
        min-height: 80vh;
        padding: 80px 0 40px 0;
        margin-bottom: 24px;
    }
    .hero-content {
        padding: 16px 4px 16px 4px;
    }
    .hero-layout {
        grid-template-columns: 1fr;
        gap: 32px;
        text-align: center;
        min-height: unset;
    }
    .hero-left {
        text-align: center;
        order: 2;
        padding: 0;
    }
    .hero-right {
        order: 1;
        padding: 0;
    }
    .hero-logo {
        max-width: 220px;
    }
    .hero-buttons {
        justify-content: center;
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }
    .date-location {
        justify-content: center;
    }
    
    .hero-btn {
        width: 100%;
        max-width: 280px;
        padding: 16px 30px;
        font-size: 14px;
    }
    
    .date-location {
        flex-direction: column;
        gap: 20px;
    }
    
    .info-item {
        width: 100%;
    }
    
    .date, .location {
        width: 100%;
        max-width: 250px;
        text-align: center;
    }
    
    .content-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .text-column {
        padding-right: 0;
    }
    
    .disciplines-content {
        grid-template-columns: 1fr;
    }
    
    .carousel {
        grid-template-columns: 1fr;
        height: auto;
    }
    
    .carousel-item {
        height: 300px;
    }
    
    .hotels-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-logos {
        flex-direction: row;
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .hero-content {
        padding: 120px 15px 0;
    }
    
    .section {
        padding: 60px 0;
    }
    
    .disciplines-section {
        padding: 80px 0;
    }
    
    .gallery {
        padding: 80px 0;
    }
}

/* Animation for scroll */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.section {
    animation: fadeInUp 0.6s ease-out;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}
