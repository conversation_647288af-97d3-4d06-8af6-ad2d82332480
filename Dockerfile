# Use official Node.js LTS image
FROM node:20-alpine

# Set working directory
WORKDIR /app

# Copy package files and install dependencies
COPY package*.json ./
RUN npm install

# Copy the rest of the project
COPY . .

# Install Tailwind, PostCSS, Autoprefixer, Vite (if not already in package.json)
RUN npm install -D tailwindcss postcss autoprefixer vite

# Initialize Tailwind config if not present
RUN [ ! -f tailwind.config.js ] && npx tailwindcss init -p || true

# Expose Vite dev server port
EXPOSE 5173

# Start Vite dev server
CMD ["npx", "vite", "--host", "0.0.0.0"]
