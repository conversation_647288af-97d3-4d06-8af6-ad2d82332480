{"version": "2.0.0", "tasks": [{"type": "shell", "label": "Install Tailwind CSS and start dev server", "command": "npm install -D tailwindcss postcss autoprefixer && npx tailwindcss init -p && npm install -D vite && echo '\nmodule.exports = { plugins: { tailwindcss: {}, autoprefixer: {} } }' > postcss.config.js && echo '\nimport \"./styles.css\";' > main.js && npx vite", "group": "build", "isBackground": false, "problemMatcher": []}]}