# World Skydiving Championship 2025 Website

A modern, responsive website for the World Skydiving Championship taking place in Spišská Nová Ves from June 15-21, 2025.

## Features

### Design & Layout
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Modern Gradient Design**: Matches the Figma wireframe with orange-blue gradient theme
- **Smooth Animations**: Parallax effects, hover animations, and scroll animations
- **Typography**: Uses Inter and Roboto fonts for a professional look

### Sections
1. **Navigation Bar**: Fixed navigation with hover effects
2. **Hero Section**: Full-screen hero with championship title and event details
3. **How to Get Here**: Information section with call-to-action button
4. **Disciplines**: Showcases Accuracy Landing and Individual Style competitions
5. **Accommodation**: Grid of hotel options near the airfield
6. **Gallery**: Carousel-style image gallery
7. **Footer**: Championship branding and logos

### Technical Features
- **HTML5 Semantic Structure**: Proper semantic elements for accessibility
- **CSS Grid & Flexbox**: Modern layout techniques
- **Custom CSS Animations**: Smooth transitions and hover effects
- **JavaScript Interactivity**: Navigation, parallax effects, and user interactions
- **Mobile-First Responsive**: Optimized for all screen sizes

## File Structure

```
/
├── index.html          # Main HTML file
├── styles.css          # All CSS styles and responsive design
├── script.js           # JavaScript functionality and interactions
└── README.md           # This documentation file
```

## Color Scheme
- **Primary Orange**: #d18232, #f1be58
- **Primary Blue**: #033f88, #0b95ff
- **Background**: #1f2633, #002351
- **Text**: #1d1b20, #49454f
- **Accent**: #feb41f

## Fonts
- **Inter**: Used for headings and hero text (weights: 300, 400, 600)
- **Roboto**: Used for body text and UI elements (weights: 400, 500, 600, 900)

## Browser Support
- Chrome, Firefox, Safari, Edge (modern versions)
- Mobile browsers (iOS Safari, Chrome Mobile)

## Usage

1. Open `index.html` in a web browser
2. The website is fully functional with:
   - Smooth scrolling navigation
   - Interactive hover effects
   - Responsive layout
   - Parallax hero section

## Customization

### Adding Images
Replace the CSS gradient backgrounds with actual images:
- Hero background: Add background-image to `.hero-background`
- Section images: Replace `.section-image` background
- Hotel images: Replace `.hotel-image` background
- Gallery images: Replace `.carousel-item` backgrounds

### Content Updates
- Edit text content in `index.html`
- Update dates, locations, and descriptions as needed
- Modify hotel information in the accommodation section

### Styling Changes
- Colors: Update CSS custom properties in `styles.css`
- Fonts: Change font families in the CSS
- Layout: Modify grid and flexbox properties for different layouts

## Performance Notes
- Uses web-safe fonts with Google Fonts fallback
- Optimized CSS with minimal external dependencies
- Lightweight JavaScript for smooth performance
- Mobile-optimized for fast loading

## Future Enhancements
- Add actual championship images
- Implement countdown timer to event
- Add registration/booking functionality
- Include competitor profiles and results
- Add multi-language support
