// Navigation functionality
document.addEventListener('DOMContentLoaded', function() {
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            // Remove active class from all items
            navItems.forEach(nav => nav.classList.remove('active'));
            // Add active class to clicked item
            this.classList.add('active');
        });
    });
    
    // Hero button functionality
    const preRegisterBtn = document.querySelector('.hero-btn.primary');
    const infoBtn = document.querySelector('.hero-btn.secondary');
    
    if (preRegisterBtn) {
        preRegisterBtn.addEventListener('click', function() {
            // TODO: Replace with actual registration form URL
            alert('Pre-registration form will open here!\n\nThis will redirect to the official registration portal.');
            // window.open('https://registration-portal.com', '_blank');
        });
    }
    
    if (infoBtn) {
        infoBtn.addEventListener('click', function() {
            // Scroll to disciplines section
            const disciplinesSection = document.querySelector('.disciplines-section');
            if (disciplinesSection) {
                disciplinesSection.scrollIntoView({ behavior: 'smooth' });
            }
        });
    }
});

// Image lazy loading and error handling
document.addEventListener('DOMContentLoaded', function() {
    const images = document.querySelectorAll('img');
    
    // Create fallback for missing images
    images.forEach(img => {
        // Log image loading attempts
        console.log('Loading image:', img.src);
        
        img.addEventListener('error', function() {
            console.log('Failed to load image:', this.src);
            // Create a placeholder using canvas
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const rect = this.getBoundingClientRect();
            
            canvas.width = rect.width || 400;
            canvas.height = rect.height || 300;
            
            // Create gradient
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#d18232');
            gradient.addColorStop(1, '#033f88');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Add text
            ctx.fillStyle = 'white';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('Image Loading...', canvas.width / 2, canvas.height / 2);
            
            // Replace image with canvas
            this.src = canvas.toDataURL();
        });
        
        img.addEventListener('load', function() {
            console.log('Successfully loaded image:', this.src);
            // Only apply opacity transition to non-hero background images
            if (!this.classList.contains('hero-bg-img')) {
                this.style.transition = 'opacity 0.3s ease';
                this.style.opacity = '1';
            }
        });
        
        // Set initial opacity only for non-hero background images
        if (!img.classList.contains('hero-bg-img')) {
            img.style.opacity = '0';
        }
    });
    
    // Intersection Observer for lazy loading
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                if (img.dataset.src) {
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    observer.unobserve(img);
                }
            }
        });
    });
    
    // Observe images for lazy loading
    document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
    });
});

// Smooth scrolling for navigation
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.scrollIntoView({ behavior: 'smooth' });
    }
}

// Parallax effect for hero section
window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const heroBackground = document.querySelector('.hero-background');
    const heroContent = document.querySelector('.hero-content');
    
    if (heroBackground) {
        heroBackground.style.transform = `translateY(${scrolled * 0.5}px)`;
    }
    
    if (heroContent) {
        heroContent.style.transform = `translateY(${scrolled * 0.2}px)`;
    }
});

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('animate-in');
        }
    });
}, observerOptions);

// Observe all sections for animation
document.addEventListener('DOMContentLoaded', function() {
    const sections = document.querySelectorAll('.section, .discipline-card, .hotel-card');
    sections.forEach(section => {
        observer.observe(section);
    });
});

// Add hover effects for discipline cards
document.addEventListener('DOMContentLoaded', function() {
    const disciplineCards = document.querySelectorAll('.discipline-card');
    
    disciplineCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05) rotateY(5deg)';
            this.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.3)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotateY(0deg)';
            this.style.boxShadow = 'none';
        });
    });
});

// Hotel card click functionality
document.addEventListener('DOMContentLoaded', function() {
    const hotelCards = document.querySelectorAll('.hotel-card');
    
    hotelCards.forEach(card => {
        card.addEventListener('click', function() {
            const hotelName = this.querySelector('.hotel-name').textContent;
            alert(`More information about ${hotelName} coming soon!`);
        });
    });
});

// Add loading animation
window.addEventListener('load', function() {
    document.body.classList.add('loaded');
});

// Navbar transparency on scroll
window.addEventListener('scroll', function() {
    const navigation = document.querySelector('.navigation');
    const scrollPosition = window.scrollY;
    
    if (scrollPosition > 100) {
        navigation.style.background = 'linear-gradient(135deg, rgba(209, 130, 50, 0.95), rgba(241, 190, 88, 0.95))';
        navigation.style.backdropFilter = 'blur(10px)';
    } else {
        navigation.style.background = 'linear-gradient(135deg, #d18232, #f1be58)';
        navigation.style.backdropFilter = 'none';
    }
});

// Countdown timer (if you want to add one later)
function createCountdown(targetDate) {
    const countdownElement = document.createElement('div');
    countdownElement.className = 'countdown';
    
    function updateCountdown() {
        const now = new Date().getTime();
        const distance = targetDate - now;
        
        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);
        
        countdownElement.innerHTML = `
            <div class="countdown-item">
                <span class="countdown-number">${days}</span>
                <span class="countdown-label">Days</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number">${hours}</span>
                <span class="countdown-label">Hours</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number">${minutes}</span>
                <span class="countdown-label">Minutes</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number">${seconds}</span>
                <span class="countdown-label">Seconds</span>
            </div>
        `;
        
        if (distance < 0) {
            countdownElement.innerHTML = "Championship has started!";
        }
    }
    
    updateCountdown();
    setInterval(updateCountdown, 1000);
    
    return countdownElement;
}

// Add some Easter eggs
let clickCount = 0;
const heroTitle = document.querySelector('.hero-title');
if (heroTitle) {
    heroTitle.addEventListener('click', function() {
        clickCount++;
        if (clickCount >= 5) {
            this.style.animation = 'bounce 0.5s ease-in-out';
            setTimeout(() => {
                this.style.animation = '';
            }, 500);
            clickCount = 0;
        }
    });
}

// Mobile menu toggle (if needed)
function toggleMobileMenu() {
    const navContainer = document.querySelector('.nav-container');
    navContainer.classList.toggle('mobile-open');
}
