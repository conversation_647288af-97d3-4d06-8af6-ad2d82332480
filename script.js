// Modern Navigation System
class ModernNavigation {
    constructor() {
        this.nav = document.querySelector('.modern-nav');
        this.navLinks = document.querySelectorAll('.nav-link');
        this.mobileToggle = document.querySelector('.mobile-menu-toggle');
        this.navLinksContainer = document.querySelector('.nav-links');
        this.themeToggle = document.querySelector('.theme-toggle');
        this.scrollProgress = document.querySelector('.scroll-progress');

        this.init();
    }

    init() {
        this.setupScrollEffects();
        this.setupMobileMenu();
        this.setupThemeToggle();
        this.setupSmoothScrolling();
        this.setupActiveNavigation();
        this.setupScrollProgress();
    }

    setupScrollEffects() {
        let lastScrollY = window.scrollY;

        window.addEventListener('scroll', () => {
            const currentScrollY = window.scrollY;

            // Add scrolled class for styling
            if (currentScrollY > 50) {
                this.nav.classList.add('scrolled');
            } else {
                this.nav.classList.remove('scrolled');
            }

            // Hide/show nav on scroll
            if (currentScrollY > lastScrollY && currentScrollY > 100) {
                this.nav.style.transform = 'translateY(-100%)';
            } else {
                this.nav.style.transform = 'translateY(0)';
            }

            lastScrollY = currentScrollY;
        });
    }

    setupMobileMenu() {
        if (this.mobileToggle) {
            this.mobileToggle.addEventListener('click', () => {
                const isOpen = this.mobileToggle.getAttribute('aria-expanded') === 'true';

                this.mobileToggle.setAttribute('aria-expanded', !isOpen);
                this.navLinksContainer.classList.toggle('mobile-open');

                // Prevent body scroll when menu is open
                document.body.style.overflow = isOpen ? 'auto' : 'hidden';
            });

            // Close mobile menu when clicking on a link
            this.navLinks.forEach(link => {
                link.addEventListener('click', () => {
                    this.mobileToggle.setAttribute('aria-expanded', 'false');
                    this.navLinksContainer.classList.remove('mobile-open');
                    document.body.style.overflow = 'auto';
                });
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!this.nav.contains(e.target) && this.navLinksContainer.classList.contains('mobile-open')) {
                    this.mobileToggle.setAttribute('aria-expanded', 'false');
                    this.navLinksContainer.classList.remove('mobile-open');
                    document.body.style.overflow = 'auto';
                }
            });
        }
    }

    setupThemeToggle() {
        if (this.themeToggle) {
            // Check for saved theme preference or default to system preference
            const savedTheme = localStorage.getItem('theme');
            const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            const initialTheme = savedTheme || (systemPrefersDark ? 'dark' : 'light');

            this.setTheme(initialTheme);

            this.themeToggle.addEventListener('click', () => {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                this.setTheme(newTheme);
            });
        }
    }

    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);

        if (this.themeToggle) {
            this.themeToggle.classList.toggle('dark', theme === 'dark');
        }
    }

    setupSmoothScrolling() {
        this.navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                const targetSection = document.getElementById(targetId);

                if (targetSection) {
                    const navHeight = this.nav.offsetHeight;
                    const targetPosition = targetSection.offsetTop - navHeight - 20;

                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            });
        });
    }

    setupActiveNavigation() {
        const sections = document.querySelectorAll('section[id]');

        const observerOptions = {
            rootMargin: `-${this.nav.offsetHeight}px 0px -50% 0px`,
            threshold: 0.1
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const activeLink = document.querySelector(`.nav-link[data-section="${entry.target.id}"]`);

                    // Remove active class from all links
                    this.navLinks.forEach(link => link.classList.remove('active'));

                    // Add active class to current link
                    if (activeLink) {
                        activeLink.classList.add('active');
                    }
                }
            });
        }, observerOptions);

        sections.forEach(section => observer.observe(section));
    }

    setupScrollProgress() {
        if (this.scrollProgress) {
            window.addEventListener('scroll', () => {
                const scrollTop = window.scrollY;
                const docHeight = document.documentElement.scrollHeight - window.innerHeight;
                const scrollPercent = scrollTop / docHeight;

                this.scrollProgress.style.transform = `scaleX(${scrollPercent})`;
            });
        }
    }
}

// Initialize modern navigation when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ModernNavigation();

    // Hero button functionality
    const preRegisterBtn = document.querySelector('.hero-btn.primary');
    const infoBtn = document.querySelector('.hero-btn.secondary');

    if (preRegisterBtn) {
        preRegisterBtn.addEventListener('click', function() {
            // TODO: Replace with actual registration form URL
            alert('Pre-registration form will open here!\n\nThis will redirect to the official registration portal.');
            // window.open('https://registration-portal.com', '_blank');
        });
    }

    if (infoBtn) {
        infoBtn.addEventListener('click', function() {
            // Scroll to disciplines section
            const disciplinesSection = document.querySelector('.disciplines-section');
            if (disciplinesSection) {
                disciplinesSection.scrollIntoView({ behavior: 'smooth' });
            }
        });
    }
});

// Image lazy loading and error handling
document.addEventListener('DOMContentLoaded', function() {
    const images = document.querySelectorAll('img');
    
    // Create fallback for missing images
    images.forEach(img => {
        // Log image loading attempts
        console.log('Loading image:', img.src);
        
        img.addEventListener('error', function() {
            console.log('Failed to load image:', this.src);
            // Create a placeholder using canvas
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const rect = this.getBoundingClientRect();
            
            canvas.width = rect.width || 400;
            canvas.height = rect.height || 300;
            
            // Create gradient
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#d18232');
            gradient.addColorStop(1, '#033f88');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Add text
            ctx.fillStyle = 'white';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('Image Loading...', canvas.width / 2, canvas.height / 2);
            
            // Replace image with canvas
            this.src = canvas.toDataURL();
        });
        
        img.addEventListener('load', function() {
            console.log('Successfully loaded image:', this.src);
            // Only apply opacity transition to non-hero background images
            if (!this.classList.contains('hero-bg-img')) {
                this.style.transition = 'opacity 0.3s ease';
                this.style.opacity = '1';
            }
        });
        
        // Set initial opacity only for non-hero background images
        if (!img.classList.contains('hero-bg-img')) {
            img.style.opacity = '0';
        }
    });
    
    // Intersection Observer for lazy loading
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                if (img.dataset.src) {
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    observer.unobserve(img);
                }
            }
        });
    });
    
    // Observe images for lazy loading
    document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
    });
});

// Smooth scrolling for navigation
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.scrollIntoView({ behavior: 'smooth' });
    }
}

// Parallax effect for hero section
window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const heroBackground = document.querySelector('.hero-background');
    const heroContent = document.querySelector('.hero-content');
    
    if (heroBackground) {
        heroBackground.style.transform = `translateY(${scrolled * 0.5}px)`;
    }
    
    if (heroContent) {
        heroContent.style.transform = `translateY(${scrolled * 0.2}px)`;
    }
});

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('animate-in');
        }
    });
}, observerOptions);

// Observe all sections for animation
document.addEventListener('DOMContentLoaded', function() {
    const sections = document.querySelectorAll('.section, .discipline-card, .hotel-card');
    sections.forEach(section => {
        observer.observe(section);
    });
});

// Modern Discipline Cards Component
class DisciplineCards {
    constructor() {
        this.cards = document.querySelectorAll('.modern-discipline-card');
        this.init();
    }

    init() {
        this.setupCardInteractions();
        this.setupIntersectionObserver();
    }

    setupCardInteractions() {
        this.cards.forEach(card => {
            // Add click handlers for buttons
            const button = card.querySelector('.discipline-btn');
            if (button) {
                button.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const action = button.getAttribute('data-action');
                    this.handleButtonClick(action);
                });
            }

            // Add card click handler
            card.addEventListener('click', (e) => {
                const discipline = card.getAttribute('data-discipline');
                this.handleCardClick(discipline, e);
            });

            // Add modern hover effects
            card.addEventListener('mouseenter', () => {
                this.addHoverEffect(card);
            });

            card.addEventListener('mouseleave', () => {
                this.removeHoverEffect(card);
            });
        });
    }

    setupIntersectionObserver() {
        const observerOptions = {
            threshold: 0.2,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                    this.animateStats(entry.target);
                }
            });
        }, observerOptions);

        this.cards.forEach(card => observer.observe(card));
    }

    addHoverEffect(card) {
        const icon = card.querySelector('.discipline-icon');
        const stats = card.querySelectorAll('.stat-item');

        if (icon) {
            icon.style.transform = 'scale(1.1) rotate(10deg)';
        }

        stats.forEach((stat, index) => {
            setTimeout(() => {
                stat.style.transform = 'translateY(-4px)';
                stat.style.background = 'rgba(255, 255, 255, 0.2)';
            }, index * 100);
        });
    }

    removeHoverEffect(card) {
        const icon = card.querySelector('.discipline-icon');
        const stats = card.querySelectorAll('.stat-item');

        if (icon) {
            icon.style.transform = '';
        }

        stats.forEach(stat => {
            stat.style.transform = '';
            stat.style.background = '';
        });
    }

    animateStats(card) {
        const statValues = card.querySelectorAll('.stat-value');

        statValues.forEach(statValue => {
            const finalValue = statValue.textContent;
            const isNumeric = !isNaN(parseInt(finalValue));

            if (isNumeric) {
                const targetNumber = parseInt(finalValue);
                let currentNumber = 0;
                const increment = targetNumber / 30;

                const counter = setInterval(() => {
                    currentNumber += increment;
                    if (currentNumber >= targetNumber) {
                        statValue.textContent = finalValue;
                        clearInterval(counter);
                    } else {
                        statValue.textContent = Math.floor(currentNumber).toString();
                    }
                }, 50);
            }
        });
    }

    handleButtonClick(action) {
        switch (action) {
            case 'learn-accuracy':
                this.showDisciplineDetails('accuracy');
                break;
            case 'learn-style':
                this.showDisciplineDetails('style');
                break;
        }
    }

    handleCardClick(discipline, event) {
        // Add ripple effect
        this.createRippleEffect(event.currentTarget, event);

        // Show discipline details after a short delay
        setTimeout(() => {
            this.showDisciplineDetails(discipline);
        }, 200);
    }

    createRippleEffect(element, event) {
        const ripple = document.createElement('div');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
            z-index: 10;
        `;

        element.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    showDisciplineDetails(discipline) {
        // This would typically open a modal or navigate to a detailed page
        const messages = {
            accuracy: 'Accuracy Landing: The ultimate test of precision where competitors aim for a 2cm target from 1000m altitude.',
            style: 'Individual Style: A choreographed sequence of aerial maneuvers judged on technique, timing, and artistic execution.'
        };

        alert(messages[discipline] || 'More details coming soon!');
    }
}

// Add CSS for ripple animation
const rippleCSS = `
@keyframes ripple {
    to {
        transform: scale(2);
        opacity: 0;
    }
}
`;

const style = document.createElement('style');
style.textContent = rippleCSS;
document.head.appendChild(style);

// Initialize discipline cards when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new DisciplineCards();
});

// Hotel card click functionality
document.addEventListener('DOMContentLoaded', function() {
    const hotelCards = document.querySelectorAll('.hotel-card');
    
    hotelCards.forEach(card => {
        card.addEventListener('click', function() {
            const hotelName = this.querySelector('.hotel-name').textContent;
            alert(`More information about ${hotelName} coming soon!`);
        });
    });
});

// Add loading animation
window.addEventListener('load', function() {
    document.body.classList.add('loaded');
});

// Navbar transparency on scroll
window.addEventListener('scroll', function() {
    const navigation = document.querySelector('.navigation');
    const scrollPosition = window.scrollY;
    
    if (scrollPosition > 100) {
        navigation.style.background = 'linear-gradient(135deg, rgba(209, 130, 50, 0.95), rgba(241, 190, 88, 0.95))';
        navigation.style.backdropFilter = 'blur(10px)';
    } else {
        navigation.style.background = 'linear-gradient(135deg, #d18232, #f1be58)';
        navigation.style.backdropFilter = 'none';
    }
});

// Countdown timer (if you want to add one later)
function createCountdown(targetDate) {
    const countdownElement = document.createElement('div');
    countdownElement.className = 'countdown';
    
    function updateCountdown() {
        const now = new Date().getTime();
        const distance = targetDate - now;
        
        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);
        
        countdownElement.innerHTML = `
            <div class="countdown-item">
                <span class="countdown-number">${days}</span>
                <span class="countdown-label">Days</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number">${hours}</span>
                <span class="countdown-label">Hours</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number">${minutes}</span>
                <span class="countdown-label">Minutes</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number">${seconds}</span>
                <span class="countdown-label">Seconds</span>
            </div>
        `;
        
        if (distance < 0) {
            countdownElement.innerHTML = "Championship has started!";
        }
    }
    
    updateCountdown();
    setInterval(updateCountdown, 1000);
    
    return countdownElement;
}

// Add some Easter eggs
let clickCount = 0;
const heroTitle = document.querySelector('.hero-title');
if (heroTitle) {
    heroTitle.addEventListener('click', function() {
        clickCount++;
        if (clickCount >= 5) {
            this.style.animation = 'bounce 0.5s ease-in-out';
            setTimeout(() => {
                this.style.animation = '';
            }, 500);
            clickCount = 0;
        }
    });
}

// Mobile menu toggle (if needed)
function toggleMobileMenu() {
    const navContainer = document.querySelector('.nav-container');
    navContainer.classList.toggle('mobile-open');
}
